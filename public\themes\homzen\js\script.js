/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
var __webpack_exports__ = {};
/*!****************************************************!*\
  !*** ./platform/themes/homzen/assets/js/script.js ***!
  \****************************************************/


function _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }
function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _iterableToArrayLimit(r, l) { var t = null == r ? null : "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t["return"] && (u = t["return"](), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }
function _arrayWithHoles(r) { if (Array.isArray(r)) return r; }
function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t["return"] || t["return"](); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
$(function () {
  window.Theme = window.Theme || {};
  window.Theme.isRtl = function () {
    return document.body.getAttribute('dir') === 'rtl';
  };
  var setCookie = function setCookie(name, value, days) {
    var expires = '';
    if (days) {
      var date = new Date();
      date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
      expires = '; expires=' + date.toUTCString();
    }
    document.cookie = name + '=' + (value || '') + expires + '; path=/';
  };
  var getCookie = function getCookie(name) {
    var nameEQ = name + '=';
    var ca = document.cookie.split(';');
    for (var _i = 0; _i < ca.length; _i++) {
      var c = ca[_i];
      while (c.charAt(0) == ' ') c = c.substring(1, c.length);
      if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);
    }
    return null;
  };
  var isMobile = {
    Android: function Android() {
      return navigator.userAgent.match(/Android/i);
    },
    BlackBerry: function BlackBerry() {
      return navigator.userAgent.match(/BlackBerry/i);
    },
    iOS: function iOS() {
      return navigator.userAgent.match(/iPhone|iPad|iPod/i);
    },
    Opera: function Opera() {
      return navigator.userAgent.match(/Opera Mini/i);
    },
    Windows: function Windows() {
      return navigator.userAgent.match(/IEMobile/i);
    },
    any: function any() {
      return isMobile.Android() || isMobile.BlackBerry() || isMobile.iOS() || isMobile.Opera() || isMobile.Windows();
    }
  };

  /* Parallax
  -------------------------------------------------------------------------------------*/
  var parallax = function parallax() {
    if ($().parallax && isMobile.any() == null) {
      $('.parallax').parallax('50%', 0.2);
    }
  };
  /* Content box
  -------------------------------------------------------------------------------------*/
  var flatContentBox = function flatContentBox() {
    $(window).on('load resize', function () {
      var mode = 'desktop';
      if (matchMedia('only screen and (max-width: 1199px)').matches) {
        mode = 'mobile';
      }
      $('.themesflat-content-box').each(function () {
        var margin = $(this).data('margin');
        if (margin) {
          if (mode === 'desktop') {
            $(this).attr('style', 'margin:' + $(this).data('margin'));
          } else if (mode === 'mobile') {
            $(this).attr('style', 'margin:' + $(this).data('mobilemargin'));
          }
        }
      });
    });
  };
  /* Counter
  -------------------------------------------------------------------------------------*/
  var flatCounter = function flatCounter() {
    var $counter = $('.tf-counter');
    if ($counter.length > 0 && $(document.body).hasClass('counter-scroll')) {
      var a = 0;
      $(window).scroll(function () {
        var oTop = $counter.offset().top - window.innerHeight;
        if (a === 0 && $(window).scrollTop() > oTop) {
          if ($().countTo) {
            $('.tf-counter').find('.number').each(function () {
              var to = $(this).data('to'),
                speed = $(this).data('speed'),
                dec = $(this).data('dec');
              $(this).countTo({
                to: to,
                speed: speed,
                decimals: dec
              });
            });
          }
          a = 1;
        }
      });
    }
  };
  new WOW().init();

  /* Sidebar Toggle
  -------------------------------------------------------------------------------------*/
  var sidebarToggle = function sidebarToggle() {
    var args = {
      duration: 500
    };
    $('.btn-show-advanced').click(function () {
      $(this).parent('.inner-filter').find('.wd-amenities').slideDown(args);
      $('.inner-filter').addClass('active');
    });
    $('.btn-hide-advanced').click(function () {
      $(this).parent('.inner-filter').find('.wd-amenities').slideUp(args);
      $('.inner-filter').removeClass('active');
    });
    $('.btn-show-advanced-mb').click(function () {
      $(this).parent('.inner-filter').find('.wd-show-filter-mb').slideToggle(args);
    });
  };
  /* Lightbox
  -------------------------------------------------------------------------------------*/
  var popUpLightBox = function popUpLightBox() {
    if ($('.lightbox-image').length) {
      $('.lightbox-image').fancybox({
        openEffect: 'fade',
        closeEffect: 'fade',
        helpers: {
          media: {}
        }
      });
    }
  };
  /* Preloader
  -------------------------------------------------------------------------------------*/
  var preloader = function preloader() {
    setTimeout(function () {
      $('.preload').fadeOut('slow', function () {
        $(this).remove();
      });
    }, 200);
  };

  /* Show Pass
  -------------------------------------------------------------------------------------*/
  var showPass = function showPass() {
    $('.show-pass').on('click', function () {
      $(this).toggleClass('active');
      if ($('.password-field').attr('type') == 'password') {
        $('.password-field').attr('type', 'text');
      } else if ($('.password-field').attr('type') == 'text') {
        $('.password-field').attr('type', 'password');
      }
    });
    $('.show-pass2').on('click', function () {
      $(this).toggleClass('active');
      if ($('.password-field2').attr('type') == 'password') {
        $('.password-field2').attr('type', 'text');
      } else if ($('.password-field2').attr('type') == 'text') {
        $('.password-field2').attr('type', 'password');
      }
    });
    $('.show-pass3').on('click', function () {
      $(this).toggleClass('active');
      if ($('.password-field3').attr('type') == 'password') {
        $('.password-field3').attr('type', 'text');
      } else if ($('.password-field3').attr('type') == 'text') {
        $('.password-field3').attr('type', 'password');
      }
    });
  };
  /* Button Quantity
  -------------------------------------------------------------------------------------*/
  var btnQuantity = function btnQuantity() {
    $('.minus-btn').on('click', function (e) {
      e.preventDefault();
      var $this = $(this);
      var $input = $this.closest('div').find('input');
      var value = parseInt($input.val());
      if (value > 0) {
        value = value - 1;
      }
      $input.val(value);
    });
    $('.plus-btn').on('click', function (e) {
      e.preventDefault();
      var $this = $(this);
      var $input = $this.closest('div').find('input');
      var value = parseInt($input.val());
      if (value > -1) {
        value = value + 1;
      }
      $input.val(value);
    });
  };

  /* Input file
  -------------------------------------------------------------------------------------*/
  var flcustominput = function flcustominput() {
    $('input[type=file]').change(function (e) {
      $(this).parents('.uploadfile').find('.file-name').text(e.target.files[0].name);
    });
  };

  /* Delete image
  -------------------------------------------------------------------------------------*/
  var delete_img = function delete_img() {
    $('.remove-file').on('click', function (e) {
      e.preventDefault();
      var $this = $(this);
      $this.closest('.file-delete').remove();
    });
  };
  /* Handle Search Form
  -------------------------------------------------------------------------------------*/
  var clickSearchForm = function clickSearchForm() {
    var widgetSearchForm = $('.wd-search-form');
    if (widgetSearchForm.length) {
      $('.pull-right').on('click', function () {
        widgetSearchForm.toggleClass('show');
      });
      $(document).on('click', '.pull-right, .offcanvas-backdrop', function (a) {
        a.preventDefault();
        if ($(a.target).closest('.pull-right, .wd-search-form').length === 0) {
          widgetSearchForm.removeClass('show');
        }
      });
    }
  };
  /* Datepicker
  -------------------------------------------------------------------------------------*/
  var datePicker = function datePicker() {
    if ($('#datepicker1').length > 0) {
      $('#datepicker1').datepicker({
        firstDay: 1,
        dateFormat: 'dd/mm/yy'
      });
    }
    if ($('#datepicker2').length > 0) {
      $('#datepicker2').datepicker({
        firstDay: 1,
        dateFormat: 'dd/mm/yy'
      });
    }
    if ($('#datepicker3').length > 0) {
      $('#datepicker3').datepicker({
        firstDay: 1,
        dateFormat: 'dd/mm/yy'
      });
    }
    if ($('#datepicker4').length > 0) {
      $('#datepicker4').datepicker({
        firstDay: 1,
        dateFormat: 'dd/mm/yy'
      });
    }
  };

  /* One Page
  -------------------------------------------------------------------------------------*/
  var onepageSingle = function onepageSingle() {
    if ($('.cate-single-tab').length) {
      var top_offset = $('.main-header').height() - 10;
      $('.cate-single-tab').onePageNav({
        currentClass: 'active',
        scrollOffset: top_offset
      });
    }
  };

  /* Handle dashboard
  -------------------------------------------------------------------------------------*/
  var showHideDashboard = function showHideDashboard() {
    $('.button-show-hide').on('click', function () {
      $('.layout-wrap').toggleClass('full-width');
    });
    $('.mobile-nav-toggler,.overlay-dashboard').on('click', function () {
      $('.layout-wrap').removeClass('full-width');
    });
  };

  /* Go Top
  -------------------------------------------------------------------------------------*/
  var goTop = function goTop() {
    if ($('div').hasClass('progress-wrap')) {
      var progressPath = document.querySelector('.progress-wrap path');
      var pathLength = progressPath.getTotalLength();
      progressPath.style.transition = progressPath.style.WebkitTransition = 'none';
      progressPath.style.strokeDasharray = pathLength + ' ' + pathLength;
      progressPath.style.strokeDashoffset = pathLength;
      progressPath.getBoundingClientRect();
      progressPath.style.transition = progressPath.style.WebkitTransition = 'stroke-dashoffset 10ms linear';
      var updateprogress = function updateprogress() {
        var scroll = $(window).scrollTop();
        var height = $(document).height() - $(window).height();
        var progress = pathLength - scroll * pathLength / height;
        progressPath.style.strokeDashoffset = progress;
      };
      updateprogress();
      $(window).scroll(updateprogress);
      var offset = 200;
      var duration = 550;
      jQuery(window).on('scroll', function () {
        if (jQuery(this).scrollTop() > offset) {
          jQuery('.progress-wrap').addClass('active-progress');
        } else {
          jQuery('.progress-wrap').removeClass('active-progress');
        }
      });
      jQuery('.progress-wrap').on('click', function (event) {
        event.preventDefault();
        jQuery('html, body').animate({
          scrollTop: 0
        }, duration);
        return false;
      });
    }
  };

  /* Cursor
  -------------------------------------------------------------------------*/
  var cursor = function cursor() {
    var myCursor = jQuery('.tf-mouse');
    if (myCursor.length) {
      if ($('body')) {
        var e = document.querySelector('.tf-mouse-inner'),
          t = document.querySelector('.tf-mouse-outer');
        var n,
          _i2 = 0,
          o = !1;
        window.onmousemove = function (s) {
          o || (t.style.transform = 'translate(' + s.clientX + 'px, ' + s.clientY + 'px)'), e.style.transform = 'translate(' + s.clientX + 'px, ' + s.clientY + 'px)', n = s.clientY, _i2 = s.clientX;
        }, e.style.visibility = 'visible', t.style.visibility = 'visible';
      }
    }
  };
  var themesflatTheme = {
    // Main init function
    init: function init() {
      this.config();
      this.events();
    },
    // Define vars for caching
    config: function config() {
      this.config = {
        $window: $(window),
        $document: $(document)
      };
    },
    // Events
    events: function events() {
      var self = this;

      // Run on document ready
      self.config.$document.on('ready', function () {
        // Retina Logos
        self.retinaLogo();
      });

      // Run on Window Load
      self.config.$window.on('load', function () {});
    }
  }; // end themesflatTheme

  // Start things up
  themesflatTheme.init();

  /* RetinaLogo
  ------------------------------------------------------------------------------------- */
  var retinaLogos = function retinaLogos() {
    var retina = window.devicePixelRatio > 1 ? true : false;
    if (retina) {
      $('#site-logo-inner').find('img').attr({
        src: 'assets/images/logo/<EMAIL>',
        width: '197',
        height: '48'
      });
      $('#logo-footer.style').find('img').attr({
        src: 'assets/images/logo/<EMAIL>',
        width: '197',
        height: '48'
      });
      $('#logo-footer.style2').find('img').attr({
        src: 'assets/images/logo/<EMAIL>',
        width: '197',
        height: '48'
      });
    }
  };

  /* Header Fixed
  ------------------------------------------------------------------------------------- */
  var headerFixed = function headerFixed() {
    if ($('header').hasClass('header-fixed')) {
      var nav = $('#header');
      if (nav.length) {
        var offsetTop = nav.offset().top,
          headerHeight = nav.height(),
          injectSpace = $('<div>', {
            height: headerHeight
          });
        injectSpace.hide();
        $(window).on('load scroll', function () {
          if ($(window).scrollTop() > 0) {
            nav.addClass('is-fixed');
            injectSpace.show();
            $('#trans-logo').attr('src', 'images/logo/<EMAIL>');
          } else {
            nav.removeClass('is-fixed');
            injectSpace.hide();
            $('#trans-logo').attr('src', 'images/logo/<EMAIL>');
          }
        });
      }
    }
  };
  $('#showlogo').prepend('<a href="index.html"><img id="theImg" src="assets/images/logo/logo2.png" /></a>');

  // =========NICE SELECT=========

  if ($.isFunction($.fn.niceSelect)) {
    $('.select_js').niceSelect();
  }
  new WOW().init();

  //Submenu Dropdown Toggle
  if ($('.main-header li.dropdown2 ul').length) {
    $('.main-header li.dropdown2').append('<div class="dropdown2-btn"></div>');

    //Dropdown Button
    $('.main-header li.dropdown2 .dropdown2-btn').on('click', function () {
      $(this).prev('ul').slideToggle(500);
    });

    //Disable dropdown parent link
    $('.navigation li.dropdown2 > a').on('click', function (e) {
      e.preventDefault();
    });

    //Disable dropdown parent link
    $('.main-header .navigation li.dropdown2 > a,.hidden-bar .side-menu li.dropdown2 > a').on('click', function (e) {
      e.preventDefault();
    });
    $('.price-block .features .arrow').on('click', function (e) {
      $(e.target.offsetParent.offsetParent.offsetParent).toggleClass('active-show-hidden');
    });
  }

  // Mobile Nav Hide Show
  if ($('.mobile-menu').length) {
    //$('.mobile-menu .menu-box').mCustomScrollbar();

    var mobileMenuContent = $('.main-header .nav-outer .main-menu').html();
    $('.mobile-menu .menu-box .menu-outer').append(mobileMenuContent);
    $('.sticky-header .main-menu').append(mobileMenuContent);

    //Hide / Show Submenu
    $('.mobile-menu .navigation > li.dropdown2 > .dropdown2-btn').on('click', function (e) {
      e.preventDefault();
      var target = $(this).parent('li').children('ul');
      var args = {
        duration: 300
      };
      if ($(target).is(':visible')) {
        $(this).parent('li').removeClass('open');
        $(target).slideUp(args);
        $(this).parents('.navigation').children('li.dropdown2').removeClass('open');
        $(this).parents('.navigation').children('li.dropdown2 > ul').slideUp(args);
        return false;
      } else {
        $(this).parents('.navigation').children('li.dropdown2').removeClass('open');
        $(this).parents('.navigation').children('li.dropdown2').children('ul').slideUp(args);
        $(this).parent('li').toggleClass('open');
        $(this).parent('li').children('ul').slideToggle(args);
      }
    });

    //3rd Level Nav
    $('.mobile-menu .navigation > li.dropdown2 > ul  > li.dropdown2 > .dropdown2-btn').on('click', function (e) {
      e.preventDefault();
      var targetInner = $(this).parent('li').children('ul');
      if ($(targetInner).is(':visible')) {
        $(this).parent('li').removeClass('open');
        $(targetInner).slideUp(500);
        $(this).parents('.navigation > ul').find('li.dropdown2').removeClass('open');
        $(this).parents('.navigation > ul').find('li.dropdown > ul').slideUp(500);
        return false;
      } else {
        $(this).parents('.navigation > ul').find('li.dropdown2').removeClass('open');
        $(this).parents('.navigation > ul').find('li.dropdown2 > ul').slideUp(500);
        $(this).parent('li').toggleClass('open');
        $(this).parent('li').children('ul').slideToggle(500);
      }
    });

    //Menu Toggle Btn
    $('.mobile-nav-toggler').on('click', function () {
      $('body').addClass('mobile-menu-visible');
    });

    //Menu Toggle Btn
    $('.mobile-menu .menu-backdrop, .close-btn').on('click', function () {
      $('body').removeClass('mobile-menu-visible');
      $('.mobile-menu .navigation > li').removeClass('open');
      $('.mobile-menu .navigation li ul').slideUp(0);
    });
    $(document).keydown(function (e) {
      if (e.keyCode === 27) {
        $('body').removeClass('mobile-menu-visible');
        $('.mobile-menu .navigation > li').removeClass('open');
        $('.mobile-menu .navigation li ul').slideUp(0);
      }
    });
  }

  /* alert box
  ------------------------------------------------------------------------------------- */
  var alertBox = function alertBox() {
    $(document).on('click', '.close', function (e) {
      $(this).closest('.flat-alert').remove();
      e.preventDefault();
    });
  };
  $(window).on('load resize', function () {
    retinaLogos();
  });
  $(document).on('submit', 'form.subscribe-form', function (e) {
    e.preventDefault();
    var $form = $(e.currentTarget);
    var $button = $form.find('button[type=submit]');
    $.ajax({
      type: 'POST',
      cache: false,
      url: $form.prop('action'),
      data: new FormData($form[0]),
      contentType: false,
      processData: false,
      beforeSend: function beforeSend() {
        return $button.prop('disabled', true).addClass('btn-loading');
      },
      success: function success(_ref) {
        var error = _ref.error,
          message = _ref.message;
        if (error) {
          Theme.showError(message);
          return;
        }
        $form.find('input[name="email"]').val('');
        Theme.showSuccess(message);
        document.dispatchEvent(new CustomEvent('newsletter.subscribed'));
      },
      error: function error(_error) {
        return Theme.handleError(_error);
      },
      complete: function complete() {
        if (typeof refreshRecaptcha !== 'undefined') {
          refreshRecaptcha();
        }
        $button.prop('disabled', false).removeClass('btn-loading');
      }
    });
  });
  var animateHeading = function animateHeading() {
    //set animation timing
    var animationDelay = 2500,
      //loading bar effect
      barAnimationDelay = 3800,
      barWaiting = barAnimationDelay - 3000,
      //3000 is the duration of the transition on the loading bar - set in the scss/css file
      //letters effect
      lettersDelay = 50,
      //type effect
      typeLettersDelay = 150,
      selectionDuration = 500,
      typeAnimationDelay = selectionDuration + 800,
      //clip effect
      revealDuration = 600,
      revealAnimationDelay = 1500;
    initHeadline();
    function initHeadline() {
      //insert <i> element for each letter of a changing word
      singleLetters($('.animationtext.letters').find('.item-text'));
      //initialise headline animation
      animateHeadline($('.animationtext'));
    }
    function singleLetters($words) {
      $words.each(function () {
        var word = $(this),
          letters = word.text().split(''),
          selected = word.hasClass('is-visible');
        for (i in letters) {
          if (word.parents('.rotate-2').length > 0) letters[i] = '<em>' + letters[i] + '</em>';
          letters[i] = selected ? '<i class="in">' + letters[i] + '</i>' : '<i>' + letters[i] + '</i>';
        }
        var newLetters = letters.join('');
        word.html(newLetters).css('opacity', 1);
      });
    }
    function animateHeadline($headlines) {
      var duration = animationDelay;
      $headlines.each(function () {
        var headline = $(this);
        if (headline.hasClass('loading-bar')) {
          duration = barAnimationDelay;
          setTimeout(function () {
            headline.find('.cd-words-wrapper').addClass('is-loading');
          }, barWaiting);
        } else if (headline.hasClass('clip')) {
          var spanWrapper = headline.find('.cd-words-wrapper'),
            newWidth = spanWrapper.width() + 10;
          spanWrapper.css('width', newWidth);
        } else if (!headline.hasClass('type')) {
          //assign to .cd-words-wrapper the width of its longest word
          var words = headline.find('.cd-words-wrapper .item-text'),
            width = 0;
          words.each(function () {
            var wordWidth = $(this).width();
            if (wordWidth > width) width = wordWidth;
          });
          headline.find('.cd-words-wrapper').css('width', width);
        }

        //trigger animation
        setTimeout(function () {
          hideWord(headline.find('.is-visible').eq(0));
        }, duration);
      });
    }
    function hideWord($word) {
      var nextWord = takeNext($word);
      if ($word.parents('.animationtext').hasClass('type')) {
        var parentSpan = $word.parent('.cd-words-wrapper');
        parentSpan.addClass('selected').removeClass('waiting');
        setTimeout(function () {
          parentSpan.removeClass('selected');
          $word.removeClass('is-visible').addClass('is-hidden').children('i').removeClass('in').addClass('out');
        }, selectionDuration);
        setTimeout(function () {
          showWord(nextWord, typeLettersDelay);
        }, typeAnimationDelay);
      } else if ($word.parents('.animationtext').hasClass('letters')) {
        var bool = $word.children('i').length >= nextWord.children('i').length;
        hideLetter($word.find('i').eq(0), $word, bool, lettersDelay);
        showLetter(nextWord.find('i').eq(0), nextWord, bool, lettersDelay);
      } else if ($word.parents('.animationtext').hasClass('clip')) {
        $word.parents('.cd-words-wrapper').animate({
          width: '2px'
        }, revealDuration, function () {
          switchWord($word, nextWord);
          showWord(nextWord);
        });
      } else if ($word.parents('.animationtext').hasClass('loading-bar')) {
        $word.parents('.cd-words-wrapper').removeClass('is-loading');
        switchWord($word, nextWord);
        setTimeout(function () {
          hideWord(nextWord);
        }, barAnimationDelay);
        setTimeout(function () {
          $word.parents('.cd-words-wrapper').addClass('is-loading');
        }, barWaiting);
      } else {
        switchWord($word, nextWord);
        setTimeout(function () {
          hideWord(nextWord);
        }, animationDelay);
      }
    }
    function showWord($word, $duration) {
      if ($word.parents('.animationtext').hasClass('type')) {
        showLetter($word.find('i').eq(0), $word, false, $duration);
        $word.addClass('is-visible').removeClass('is-hidden');
      } else if ($word.parents('.animationtext').hasClass('clip')) {
        $word.parents('.cd-words-wrapper').animate({
          width: $word.width() + 10
        }, revealDuration, function () {
          setTimeout(function () {
            hideWord($word);
          }, revealAnimationDelay);
        });
      }
    }
    function hideLetter($letter, $word, $bool, $duration) {
      $letter.removeClass('in').addClass('out');
      if (!$letter.is(':last-child')) {
        setTimeout(function () {
          hideLetter($letter.next(), $word, $bool, $duration);
        }, $duration);
      } else if ($bool) {
        setTimeout(function () {
          hideWord(takeNext($word));
        }, animationDelay);
      }
      if ($letter.is(':last-child') && $('html').hasClass('no-csstransitions')) {
        var nextWord = takeNext($word);
        switchWord($word, nextWord);
      }
    }
    function showLetter($letter, $word, $bool, $duration) {
      $letter.addClass('in').removeClass('out');
      if (!$letter.is(':last-child')) {
        setTimeout(function () {
          showLetter($letter.next(), $word, $bool, $duration);
        }, $duration);
      } else {
        if ($word.parents('.animationtext').hasClass('type')) {
          setTimeout(function () {
            $word.parents('.cd-words-wrapper').addClass('waiting');
          }, 200);
        }
        if (!$bool) {
          setTimeout(function () {
            hideWord($word);
          }, animationDelay);
        }
      }
    }
    function takeNext($word) {
      return !$word.is(':last-child') ? $word.next() : $word.parent().children().eq(0);
    }
    function takePrev($word) {
      return !$word.is(':first-child') ? $word.prev() : $word.parent().children().last();
    }
    function switchWord($oldWord, $newWord) {
      $oldWord.removeClass('is-visible').addClass('is-hidden');
      $newWord.removeClass('is-hidden').addClass('is-visible');
    }
  };
  var rangeSlider = function rangeSlider() {
    if (typeof wNumb === 'undefined' || typeof noUiSlider === 'undefined') {
      return;
    }
    var priceSlider = function priceSlider() {
      $('.noUi-handle').on('click', function () {
        $(this).width(50);
      });
      $('[data-bb-toggle="range"]').each(function (index, el) {
        var $element = $(el);
        var rangeSlider = $element.find('[data-bb-toggle="range-slider"]').get(0);
        var $minInput = $element.find('.slider-labels input[data-bb-toggle="min-input"]');
        var $maxInput = $element.find('.slider-labels input[data-bb-toggle="max-input"]');
        var currencySymbol = $(rangeSlider).data('currency-symbol') || '$';
        var moneyFormatOptions = {
          decimals: 0,
          thousand: ','
        };
        var currencyWithSpace = $(rangeSlider).data('currency-with-space');
        if ($(rangeSlider).data('currency-prefix-symbol')) {
          moneyFormatOptions.prefix = currencySymbol + (currencyWithSpace ? ' ' : '');
        } else {
          moneyFormatOptions.postfix = (currencyWithSpace ? ' ' : '') + currencySymbol;
        }
        var moneyFormat = wNumb(moneyFormatOptions);
        noUiSlider.create(rangeSlider, {
          start: [parseInt($minInput.val() || $element.data('min')) || 0, parseInt($maxInput.val() || $element.data('max')) || 0],
          step: 1,
          range: {
            min: [parseInt($element.data('min'))],
            max: [parseInt($element.data('max'))]
          },
          format: moneyFormat,
          connect: true
        });
        rangeSlider.noUiSlider.on('update', function (values, handle) {
          $element.find('[data-bb-toggle="range-from-value"]').html(values[0]);
          $element.find('[data-bb-toggle="range-to-value"]').html(values[1]);
        });
        rangeSlider.noUiSlider.on('change', function (values) {
          $minInput.val(moneyFormat.from(values[0])).trigger('change');
          $maxInput.val(moneyFormat.from(values[1])).trigger('change');
        });
      });
    };
    var squareSlider = function squareSlider() {
      $('.noUi-handle2').on('click', function () {
        $(this).width(50);
      });
      var rangeSlider = $('#slider-range2').get(0);
      if (!rangeSlider) {
        return;
      }
      var unit = $(rangeSlider).data('unit');
      var moneyFormat = wNumb({
        decimals: 0,
        thousand: ',',
        postfix: unit ? " ".concat($(rangeSlider).data('unit')) : ''
      });
      var $minSquare = $('.slider-labels input[name="min_square"]');
      var $maxSquare = $('.slider-labels input[name="max_square"]');
      noUiSlider.create(rangeSlider, {
        start: [parseInt($minSquare.val() || $(rangeSlider).data('min')), parseInt($maxSquare.val() || $(rangeSlider).data('max'))],
        step: 1,
        range: {
          min: [$(rangeSlider).data('min')],
          max: [$(rangeSlider).data('max')]
        },
        format: moneyFormat,
        connect: true
      });
      rangeSlider.noUiSlider.on('update', function (values, handle) {
        document.getElementById('slider-range-value01').innerHTML = values[0];
        document.getElementById('slider-range-value02').innerHTML = values[1];
      });
      rangeSlider.noUiSlider.on('change', function (values) {
        $('.slider-labels input[name="min_square"]').val(moneyFormat.from(values[0])).trigger('change');
        $('.slider-labels input[name="max_square"]').val(moneyFormat.from(values[1])).trigger('change');
      });
    };
    var flatSlider = function flatSlider() {
      var rangeSlider = $('#slider-flat').get(0);
      if (!rangeSlider) {
        return;
      }
      var unit = $(rangeSlider).data('unit');
      var moneyFormat = wNumb({
        decimals: 0,
        thousand: ',',
        postfix: unit ? " ".concat($(rangeSlider).data('unit')) : ''
      });
      var $minFlat = $('.slider-labels input[name="min_flat"]');
      var $maxFlat = $('.slider-labels input[name="max_flat"]');
      noUiSlider.create(rangeSlider, {
        start: [parseInt($minFlat.val() || $(rangeSlider).data('min')), parseInt($maxFlat.val() || $(rangeSlider).data('max'))],
        step: 1,
        range: {
          min: [$(rangeSlider).data('min')],
          max: [$(rangeSlider).data('max')]
        },
        format: moneyFormat,
        connect: true
      });
      rangeSlider.noUiSlider.on('update', function (values, handle) {
        document.getElementById('slider-flat-value01').innerHTML = values[0];
        document.getElementById('slider-flat-value02').innerHTML = values[1];
      });
      rangeSlider.noUiSlider.on('change', function (values) {
        $('.slider-labels input[name="min_flat"]').val(moneyFormat.from(values[0])).trigger('change');
        $('.slider-labels input[name="max_flat"]').val(moneyFormat.from(values[1])).trigger('change');
      });
    };
    priceSlider();
    squareSlider();
    flatSlider();
  };
  rangeSlider();
  headerFixed();
  alertBox();
  flatContentBox();
  popUpLightBox();
  parallax();
  flatCounter();
  flcustominput();
  btnQuantity();
  delete_img();
  clickSearchForm();
  sidebarToggle();
  onepageSingle();
  showHideDashboard();
  goTop();
  showPass();
  datePicker();
  preloader();
  // cursor();
  animateHeading();
  var Spanizer = function () {
    var settings = {
      letters: $('.js-letters')
    };
    return {
      init: function init() {
        this.bind();
      },
      bind: function bind() {
        Spanizer.doSpanize();
      },
      doSpanize: function doSpanize() {
        settings.letters.html(function (i, el) {
          var spanize = $.trim(el).split('');
          return "<span>".concat(spanize.join('</span><span>'), "</span>");
        });
      }
    };
  }();
  // Let's GO!

  if (matchMedia('only screen and (min-width: 991px)').matches) {
    Spanizer.init();
  }
  if ($('.thumbs-swiper-column').length > 0) {
    var swiperthumbs = new Swiper('.thumbs-swiper-column1', {
      rtl: Theme.isRtl(),
      spaceBetween: 0,
      slidesPerView: 4,
      freeMode: true,
      direction: 'vertical',
      watchSlidesProgress: true
    });
    var swiper2 = new Swiper('.thumbs-swiper-column', {
      rtl: Theme.isRtl(),
      spaceBetween: 0,
      autoplay: {
        delay: 3000,
        disableOnInteraction: false
      },
      speed: 500,
      effect: 'fade',
      fadeEffect: {
        crossFade: true
      },
      thumbs: {
        swiper: swiperthumbs
      }
    });
  }
  if ($('.slider-sw-home2').length > 0) {
    var _swiper = new Swiper('.slider-sw-home2', {
      rtl: Theme.isRtl(),
      spaceBetween: 0,
      autoplay: {
        delay: 2000,
        disableOnInteraction: false
      },
      speed: 2000,
      effect: 'fade',
      fadeEffect: {
        crossFade: true
      }
    });
  }
  if ($('.tf-sw-auto').length > 0) {
    var loop = $('.tf-sw-auto').data('loop');
    var swiper = new Swiper('.tf-sw-auto', {
      rtl: Theme.isRtl(),
      autoplay: {
        delay: 1500,
        disableOnInteraction: false,
        pauseOnMouseEnter: true
      },
      speed: 2000,
      slidesPerView: 'auto',
      spaceBetween: 0,
      loop: loop,
      navigation: {
        clickable: true,
        nextEl: '.nav-prev-category',
        prevEl: '.nav-next-category'
      }
    });
  }
  var pagithumbs = new Swiper('.thumbs-sw-pagi', {
    rtl: Theme.isRtl(),
    spaceBetween: 14,
    slidesPerView: 'auto',
    freeMode: true,
    watchSlidesProgress: true,
    breakpoints: {
      375: {
        slidesPerView: 3,
        spaceBetween: 14
      },
      500: {
        slidesPerView: 'auto'
      }
    }
  });
  var swiperSingle = new Swiper('.sw-single', {
    rtl: Theme.isRtl(),
    spaceBetween: 16,
    autoplay: {
      delay: 3000,
      disableOnInteraction: false
    },
    speed: 500,
    effect: 'fade',
    fadeEffect: {
      crossFade: true
    },
    thumbs: {
      swiper: pagithumbs
    },
    navigation: {
      clickable: true,
      nextEl: '.nav-prev-single',
      prevEl: '.nav-next-single'
    }
  });
  if ($('.tf-latest-property').length > 0) {
    var previewLg = $('.tf-latest-property').data('preview-lg');
    var previewMd = $('.tf-latest-property').data('preview-md');
    var previewSm = $('.tf-latest-property').data('preview-sm');
    var spacing = $('.tf-latest-property').data('space');
    var centered = $('.tf-latest-property').data('centered');
    var _loop = $('.tf-latest-property').data('loop');
    var _swiper2 = new Swiper('.tf-latest-property', {
      rtl: Theme.isRtl(),
      autoplay: {
        delay: 2000,
        disableOnInteraction: false,
        reverseDirection: false
      },
      speed: 3000,
      slidesPerView: 1,
      loop: _loop,
      spaceBetween: spacing,
      centeredSlides: centered,
      breakpoints: {
        600: {
          slidesPerView: previewSm,
          spaceBetween: 20,
          centeredSlides: false
        },
        991: {
          slidesPerView: previewMd,
          spaceBetween: 20,
          centeredSlides: false
        },
        1550: {
          slidesPerView: previewLg,
          spaceBetween: spacing
        }
      }
    });
  }
  var initImageSlider = function initImageSlider() {
    if ($('.tf-sw-partner').length > 0) {
      var $element = $('.tf-sw-partner');
      var _previewLg = $element.data('preview-lg');
      var _previewMd = $element.data('preview-md');
      var _previewSm = $element.data('preview-sm');
      var _spacing = $element.data('space');
      var autoplay = $element.data('autoplay');
      var autoplaySpeed = $element.data('autoplay-speed');
      var _loop2 = $element.data('loop');
      var _swiper3 = new Swiper('.tf-sw-partner', {
        rtl: Theme.isRtl(),
        autoplay: autoplay ? {
          delay: autoplaySpeed,
          disableOnInteraction: false,
          pauseOnMouseEnter: true
        } : false,
        slidesPerView: 2,
        loop: _loop2,
        spaceBetween: 30,
        speed: 3000,
        pagination: {
          el: '.swiper-pagination',
          clickable: true
        },
        breakpoints: {
          450: {
            slidesPerView: _previewSm,
            spaceBetween: 30
          },
          768: {
            slidesPerView: _previewMd,
            spaceBetween: 30
          },
          992: {
            slidesPerView: _previewLg,
            spaceBetween: _spacing
          }
        }
      });
    }
    $('.tf-sw-partner').hover(function () {
      this.swiper.autoplay.stop();
    }, function () {
      this.swiper.autoplay.start();
    });
  };
  var initPropertyCategories = function initPropertyCategories() {
    if ($('.tf-sw-categories').length > 0) {
      var $element = $('.tf-sw-categories');
      var _previewLg2 = $element.data('preview-lg');
      var _previewMd2 = $element.data('preview-md');
      var _previewSm2 = $element.data('preview-sm');
      var _spacing2 = $element.data('space');
      var autoplay = $element.data('autoplay');
      var autoplaySpeed = $element.data('autoplay-speed');
      var _loop3 = $element.data('loop');
      var _swiper4 = new Swiper('.tf-sw-categories', {
        rtl: Theme.isRtl(),
        slidesPerView: 2,
        spaceBetween: 30,
        loop: _loop3,
        autoplay: autoplay ? {
          delay: autoplaySpeed
        } : false,
        navigation: {
          clickable: true,
          nextEl: '.nav-prev-category',
          prevEl: '.nav-next-category'
        },
        pagination: {
          el: '.sw-pagination-category',
          clickable: true
        },
        breakpoints: {
          600: {
            slidesPerView: _previewSm2,
            spaceBetween: 30
          },
          800: {
            slidesPerView: _previewMd2,
            spaceBetween: 30
          },
          1300: {
            slidesPerView: _previewLg2,
            spaceBetween: _spacing2
          }
        }
      });
    }
  };
  var initTestimonials = function initTestimonials() {
    if ($('.tf-sw-testimonial').length > 0) {
      var $element = $('.tf-sw-testimonial');
      var _previewLg3 = $element.data('preview-lg');
      var _previewMd3 = $element.data('preview-md');
      var _previewSm3 = $element.data('preview-sm');
      var _spacing3 = $element.data('space');
      var autoplay = $element.data('autoplay');
      var autoplaySpeed = $element.data('autoplay-speed');
      var _loop4 = $element.data('loop');
      var swTestimonial = new Swiper('.tf-sw-testimonial', {
        rtl: Theme.isRtl(),
        loop: _loop4,
        autoplay: autoplay ? {
          delay: autoplaySpeed
        } : false,
        slidesPerView: 1,
        spaceBetween: _spacing3,
        navigation: {
          clickable: true,
          nextEl: '.nav-prev-testimonial',
          prevEl: '.nav-next-testimonial'
        },
        pagination: {
          el: '.sw-pagination-testimonial',
          clickable: true
        },
        breakpoints: {
          768: {
            slidesPerView: _previewSm3,
            spaceBetween: 20
          },
          991: {
            slidesPerView: _previewMd3,
            spaceBetween: 20
          },
          1550: {
            slidesPerView: _previewLg3,
            spaceBetween: _spacing3
          }
        }
      });
    }
  };
  var initLocation = function initLocation() {
    if ($('.tf-sw-location').length > 0) {
      var $element = $('.tf-sw-location');
      var _previewLg4 = $element.data('preview-lg');
      var _previewMd4 = $element.data('preview-md');
      var _previewSm4 = $element.data('preview-sm');
      var _spacing4 = $element.data('space');
      var _centered = $element.data('centered');
      var autoplay = $element.data('autoplay');
      var autoplaySpeed = $element.data('autoplay-speed');
      var _loop5 = $element.data('loop');
      var _swiper5 = new Swiper('.tf-sw-location', {
        rtl: Theme.isRtl(),
        autoplay: autoplay ? {
          delay: autoplaySpeed,
          disableOnInteraction: false
        } : false,
        speed: 750,
        navigation: {
          clickable: true,
          nextEl: '.nav-prev-location',
          prevEl: '.nav-next-location'
        },
        pagination: {
          el: '.swiper-pagination1',
          clickable: true
        },
        slidesPerView: 1,
        loop: _loop5,
        spaceBetween: _spacing4,
        centeredSlides: _centered,
        breakpoints: {
          600: {
            slidesPerView: _previewSm4,
            spaceBetween: 20,
            centeredSlides: false
          },
          991: {
            slidesPerView: _previewMd4,
            spaceBetween: 20,
            centeredSlides: false
          },
          1520: {
            slidesPerView: _previewLg4,
            spaceBetween: _spacing4
          }
        }
      });
    }
  };
  var initPropertiesTab = function initPropertiesTab() {
    $(document).on('click', '[data-bb-toggle="properties-tab"] [data-bs-toggle="tab"]', function (e) {
      var currentTarget = $(e.currentTarget);
      var tab = currentTarget.closest('[data-bb-toggle="properties-tab"]');
      var data = tab.data('attributes');
      data['category_id'] = currentTarget.data('bb-value');
      $.ajax({
        url: tab.data('url'),
        method: 'GET',
        dataType: 'json',
        data: data,
        beforeSend: function beforeSend() {
          $('.flat-tab-recommended').append('<div class="loading-spinner"></div>');
        },
        success: function success(_ref2) {
          var data = _ref2.data;
          $('[data-bb-toggle="properties-tab-slot"]').html(data);
          if (typeof Theme.lazyLoadInstance !== 'undefined') {
            Theme.lazyLoadInstance.update();
          }
          initWishlist();
        },
        error: function error(_error2) {
          return Theme.handleError(_error2);
        },
        complete: function complete() {
          return $('.flat-tab-recommended').find('.loading-spinner').remove();
        }
      });
    });
    $('[data-bb-toggle="properties-tab"] [data-bs-toggle="tab"]').first().trigger('click');
  };
  var initServices = function initServices() {
    if ($('.tf-sw-benefit').length > 0) {
      new Swiper('.tf-sw-benefit', {
        rtl: Theme.isRtl(),
        slidesPerView: 1,
        spaceBetween: 30,
        navigation: {
          clickable: true,
          nextEl: '.nav-prev-benefit',
          prevEl: '.nav-next-benefit'
        },
        pagination: {
          el: '.sw-pagination-benefit',
          clickable: true
        }
      });
    }
  };
  function cleanFormData(formDataInput) {
    var formData = formDataInput.filter(function (item) {
      return item.value !== '' && (item.name !== 'per_page' || item.name === 'per_page' && parseInt(item.value) !== 12);
    });
    var queryString = formData.filter(function (item) {
      return item.name !== '_token';
    }).map(function (item) {
      return "".concat(encodeURIComponent(item.name), "=").concat(encodeURIComponent(item.value));
    });
    queryString = queryString.length > 0 ? "?".concat(queryString.join('&')) : '';
    return {
      formData: formData,
      queryString: queryString
    };
  }
  var initProperties = function initProperties() {
    if ($('.tf-sw-property').length > 0) {
      new Swiper('.tf-sw-property', {
        rtl: Theme.isRtl(),
        slidesPerView: 1,
        spaceBetween: 30,
        navigation: {
          clickable: true,
          nextEl: '.nav-prev-property',
          prevEl: '.nav-next-property'
        },
        pagination: {
          el: '.sw-pagination-property',
          clickable: true
        }
      });
    }
  };
  initImageSlider();
  initImageSlider();
  initLocation();
  initPropertiesTab();
  initPropertyCategories();
  initProperties();
  initServices();
  $('[data-bb-toggle="detail-map"]').each(function (index, element) {
    var $element = $(element);
    var map = L.map($element.prop('id'), {
      attributionControl: false
    }).setView($element.data('center'), 14);
    L.tileLayer($element.data('tile-layer'), {
      maxZoom: $element.data('max-zoom') || 22
    }).addTo(map);
    L.marker($element.data('center'), {
      icon: L.divIcon({
        iconSize: L.point(50, 50),
        className: 'map-marker-home'
      })
    }).addTo(map).bindPopup($('#map-popup-content').html()).openPopup();
    if (typeof Theme.lazyLoadInstance !== 'undefined') {
      Theme.lazyLoadInstance.update();
    }
  });
  var initMap = function initMap(formData) {
    var $element = $('[data-bb-toggle="list-map"]');
    if ($element.length < 1) {
      return;
    }
    if (window.activeMap) {
      window.activeMap.remove();
    }
    var center = $element.data('center');
    var centerFirst = $('.homeya-box[data-lat][data-lng]').filter(function (index, item) {
      return $(item).data('lat') && $(item).data('lng');
    });
    if (centerFirst && centerFirst.length) {
      center = [centerFirst.data('lat'), centerFirst.data('lng')];
    }
    var map = L.map($element.prop('id'), {
      attributionControl: false
    }).setView(center, 14);
    L.tileLayer($element.data('tile-layer'), {
      maxZoom: $element.data('max-zoom') || 22
    }).addTo(map);
    var totalPage = 0;
    var currentPage = 1;
    var markers = L.markerClusterGroup();
    var populate = function populate() {
      if (typeof formData === 'undefined') {
        var urlParams = new URLSearchParams(window.location.search);
        formData = {};
        if (urlParams.size > 0) {
          var _iterator = _createForOfIteratorHelper(urlParams),
            _step;
          try {
            for (_iterator.s(); !(_step = _iterator.n()).done;) {
              var _step$value = _slicedToArray(_step.value, 2),
                key = _step$value[0],
                value = _step$value[1];
              formData[key] = value;
            }
          } catch (err) {
            _iterator.e(err);
          } finally {
            _iterator.f();
          }
        } else {
          formData = {
            page: 1
          };
        }
      } else if (Array.isArray(formData)) {
        formData = formData.reduce(function (acc, _ref3) {
          var name = _ref3.name,
            value = _ref3.value;
          acc[name] = value;
          return acc;
        }, {});
      }
      formData.page = currentPage;
      if (totalPage === 0 || currentPage <= totalPage) {
        $.ajax({
          url: $element.data('url'),
          type: 'GET',
          data: formData,
          success: function success(_ref4) {
            var data = _ref4.data,
              meta = _ref4.meta;
            if (data.length < 1) {
              return;
            }
            data.forEach(function (item) {
              if (!item.latitude || !item.longitude) {
                return;
              }
              var isProperty = typeof item.square !== 'undefined';
              var content = isProperty ? $('#property-map-content').html() : $('#project-map-content').html();
              content = content.replace(new RegExp('__name__', 'gi'), item.name).replace(new RegExp('__location__', 'gi'), item.location).replace(new RegExp('__image__', 'gi'), item.image_thumb).replace(new RegExp('__price__', 'gi'), item.formatted_price).replace(new RegExp('__url__', 'gi'), item.url).replace(new RegExp('__status__', 'gi'), item.status_html);
              if (isProperty) {
                content = content.replace(new RegExp('__bedroom__', 'gi'), item.number_bedroom).replace(new RegExp('__bathroom__', 'gi'), item.number_bathroom).replace(new RegExp('__square__', 'gi'), item.square_text);
              }
              var marker = L.marker(L.latLng(item.latitude, item.longitude), {
                icon: L.divIcon({
                  iconSize: L.point(50, 20),
                  className: 'boxmarker',
                  html: item.map_icon
                })
              }).bindPopup(content, {
                maxWidth: '100%'
              }).addTo(map);
              markers.addLayer(marker);
              map.flyToBounds(markers.getBounds());
            });
            if (totalPage === 0) {
              totalPage = meta.last_page;
            }
            currentPage++;
            populate();
          }
        });
      }
    };
    populate();
    map.addLayer(markers);
    window.activeMap = map;
  };
  initMap();
  var projectSearchTimeout = null;
  var initWishlistCount = function initWishlistCount() {
    var wishlist = decodeURIComponent(getCookie('wishlist') || '');
    var projectWishlist = decodeURIComponent(getCookie('project_wishlist') || '');
    var wishlistArray = wishlist ? wishlist.split(',') : [];
    var projectWishlistArray = projectWishlist ? projectWishlist.split(',') : [];
    $('[data-bb-toggle="wishlist-count"]').text(wishlistArray.length + projectWishlistArray.length);
  };
  var initWishlist = function initWishlist() {
    var wishlist = decodeURIComponent(getCookie('wishlist') || '');
    var projectWishlist = decodeURIComponent(getCookie('project_wishlist') || '');
    var wishlistArray = wishlist ? wishlist.split(',') : [];
    var projectWishlistArray = projectWishlist ? projectWishlist.split(',') : [];
    wishlistArray.forEach(function (id) {
      $("[data-bb-toggle=\"add-to-wishlist\"][data-type=\"property\"][data-id=\"".concat(id, "\"]")).addClass('active').html("\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"currentColor\" class=\"icon\">\n                    <path stroke=\"none\" d=\"M0 0h24v24H0z\" fill=\"none\"/>\n                    <path d=\"M6.979 3.074a6 6 0 0 1 4.988 1.425l.037 .033l.034 -.03a6 6 0 0 1 4.733 -1.44l.246 .036a6 6 0 0 1 3.364 10.008l-.18 .185l-.048 .041l-7.45 7.379a1 1 0 0 1 -1.313 .082l-.094 -.082l-7.493 -7.422a6 6 0 0 1 3.176 -10.215z\" />\n                </svg>\n            ");
    });
    projectWishlistArray.forEach(function (id) {
      $("[data-bb-toggle=\"add-to-wishlist\"][data-type=\"project\"][data-id=\"".concat(id, "\"]")).addClass('active').html("\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"currentColor\" class=\"icon\">\n                    <path stroke=\"none\" d=\"M0 0h24v24H0z\" fill=\"none\"/>\n                    <path d=\"M6.979 3.074a6 6 0 0 1 4.988 1.425l.037 .033l.034 -.03a6 6 0 0 1 4.733 -1.44l.246 .036a6 6 0 0 1 3.364 10.008l-.18 .185l-.048 .041l-7.45 7.379a1 1 0 0 1 -1.313 .082l-.094 -.082l-7.493 -7.422a6 6 0 0 1 3.176 -10.215z\" />\n                </svg>\n            ");
    });
    initWishlistCount();
  };
  initWishlist();
  $(document).on('submit', '.contact-form', function (event) {
    event.preventDefault();
    event.stopPropagation();
    var $form = $(this);
    var $button = $form.find('button[type=submit]');
    $.ajax({
      type: 'POST',
      cache: false,
      url: $form.prop('action'),
      data: new FormData($form[0]),
      contentType: false,
      processData: false,
      beforeSend: function beforeSend() {
        return $button.addClass('btn-loading');
      },
      success: function success(_ref5) {
        var error = _ref5.error,
          message = _ref5.message;
        if (!error) {
          $form[0].reset();
          Theme.showSuccess(message);
        } else {
          Theme.showError(message);
        }
      },
      error: function error(_error3) {
        Theme.handleError(_error3);
      },
      complete: function complete() {
        if (typeof refreshRecaptcha !== 'undefined') {
          refreshRecaptcha();
        }
        $button.removeClass('btn-loading');
      }
    });
  }).on('change', '.filter-form select[name="sort_by"], .filter-form select[name="per_page"]', function (e) {
    $(e.currentTarget).closest('form').trigger('submit');
  }).on('click', '[data-bb-toggle="change-layout"]', function (e) {
    var $button = $(e.currentTarget);
    var $form = $button.closest('form');
    $form.find('input[name="layout"]').val($button.data('value'));
  }).on('click', '.filter-form .flat-pagination a', function (e) {
    e.preventDefault();
    var url = new URL(e.currentTarget.href);
    var $form = $(e.currentTarget).closest('form');
    $form.find('input[name="page"]').val(url.searchParams.get('page'));
    $form.trigger('submit');
  }).on('submit', '.filter-form', function (e) {
    e.preventDefault();
    $('.wd-search-form').removeClass('show');
    $('.search-box-offcanvas').removeClass('active');
    var $dataListing = $('[data-bb-toggle="data-listing"]');
    var $form = $(e.currentTarget);
    var cleanedFormData = cleanFormData($form.serializeArray());
    var nextHref = $form.prop('action') + cleanedFormData.queryString;
    $.ajax({
      url: $form.data('url') || $form.prop('action'),
      type: 'POST',
      data: cleanedFormData.formData,
      beforeSend: function beforeSend() {
        $dataListing.append('<div class="loading-spinner"></div>');
      },
      success: function success(_ref6) {
        var error = _ref6.error,
          data = _ref6.data,
          message = _ref6.message;
        if (error) {
          Theme.showError(message);
          return;
        }
        $dataListing.html(data);
        if (typeof Theme.lazyLoadInstance !== 'undefined') {
          Theme.lazyLoadInstance.update();
        }
        initMap(cleanedFormData.formData);
        if (nextHref !== window.location.href) {
          window.history.pushState(cleanedFormData.formData, message, nextHref);
          $('.reset-filter-btn').show();
        }
      },
      complete: function complete() {
        $dataListing.find('.loading-spinner').remove();
        $('html, body').animate({
          scrollTop: $dataListing.offset().top - 100
        });
      }
    });
  }).on('submit', '#hero-search-form', function (e) {
    e.preventDefault();
    var $form = $(e.currentTarget);
    var cleanedFormData = cleanFormData($form.serializeArray());
    window.location.href = $form.prop('action') + cleanedFormData.queryString;
  }).on('keyup', '[data-bb-toggle="search-suggestion"] input[type="text"]', function (e) {
    clearTimeout(projectSearchTimeout);
    var $currentTarget = $(e.currentTarget);
    var $suggest = $currentTarget.closest('[data-bb-toggle="search-suggestion"]').find('[data-bb-toggle="data-suggestion"]');
    var $form = $currentTarget.closest('form');
    var cleanedFormData = cleanFormData($form.serializeArray());
    cleanedFormData.formData.push({
      name: 'minimal',
      value: true
    });
    projectSearchTimeout = setTimeout(function () {
      $.ajax({
        url: $currentTarget.data('url') || $currentTarget.closest('form').prop('action'),
        type: 'GET',
        data: cleanedFormData.formData,
        success: function success(_ref7) {
          var data = _ref7.data;
          $suggest.html(data).slideDown();
          if (typeof Theme.lazyLoadInstance !== 'undefined') {
            Theme.lazyLoadInstance.update();
          }
        }
      });
    }, 500);
  }).on('click', '.search-suggestion-item:not([data-no-prevent])', function (e) {
    var $currentTarget = $(e.currentTarget);
    var $search = $currentTarget.closest('[data-bb-toggle="search-suggestion"]');
    var $hiddenInput = $search.find('input[type="hidden"]');
    $search.find('input[type="text"]').val($currentTarget.text());
    if ($hiddenInput.length > 0) {
      $hiddenInput.val($currentTarget.data('value')).trigger('change');
    }
    $search.find('[data-bb-toggle="data-suggestion"]').hide();
  }).on('keydown', '[data-bb-toggle="search-suggestion"] input[type="text"]', function (e) {
    $(e.currentTarget).closest('[data-bb-toggle="search-suggestion"]').find('[data-bb-toggle="data-suggestion"]').slideUp();
  }).on('click', function (e) {
    if (!$(e.target).closest('[data-bb-toggle="data-suggestion"]').length) {
      $('[data-bb-toggle="data-suggestion"]').slideUp();
    }
  }).on('click', '[data-bb-toggle="change-search-type"]', function (e) {
    var currentTarget = $(e.currentTarget);
    var form = currentTarget.closest('.flat-tab').find('form');
    form.find('input[name="type"]').val(currentTarget.data('value')).trigger('change');
    form.prop('action', currentTarget.data('url'));
    form.find('input[name="k"]').attr('data-url', currentTarget.data('url'));
    if (currentTarget.data('value') === 'project') {
      $('.project-search-form').show();
      $('.property-search-form').hide();
      $('.project-search-form input').prop('disabled', false);
      $('.project-search-form select').prop('disabled', false);
      $('.property-search-form input').prop('disabled', true);
      $('.property-search-form select').prop('disabled', true);
    } else {
      $('.project-search-form').hide();
      $('.property-search-form').show();
      $('.project-search-form input').prop('disabled', true);
      $('.project-search-form select').prop('disabled', true);
      $('.property-search-form input').prop('disabled', false);
      $('.property-search-form select').prop('disabled', false);
    }
  }).on('click', '[data-bb-toggle="add-to-wishlist"]', function (e) {
    e.preventDefault();
    var $currentTarget = $(e.currentTarget);
    var id = $currentTarget.data('id');
    var cookieName = $currentTarget.data('type') === 'property' ? 'wishlist' : 'project_wishlist';
    var wishlist = decodeURIComponent(getCookie(cookieName) || '');
    var wishlistArray = wishlist ? wishlist.split(',') : [];
    if (wishlistArray.includes(String(id))) {
      wishlistArray.splice(wishlistArray.indexOf(id), 1);
      $currentTarget.removeClass('x-favorite--active').html("\n                    <svg class=\"x-favorite_icon x-favorite-notFilled\" width=\"20\" height=\"18\" viewBox=\"0 0 20 18\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M2.43762 3.55751C1.79286 4.66283 1.58065 6.19655 1.97681 7.7812C2.11937 8.35136 2.53381 9.08448 3.19064 9.9283C3.83472 10.7557 4.65681 11.6207 5.52752 12.4505C7.25399 14.0962 9.11202 15.5477 10 16.219C10.888 15.5477 12.746 14.0962 14.4724 12.4505C15.3432 11.6207 16.1653 10.7557 16.8093 9.9283C17.4662 9.08448 17.8806 8.35136 18.0232 7.7812C18.4193 6.19655 18.2072 4.66284 17.5624 3.55751C16.933 2.47865 15.8822 1.77704 14.4426 1.77704C13.1901 1.77704 12.3074 2.70312 11.6832 3.95143C11.3833 4.55124 11.1795 5.16002 11.0505 5.62445C10.9864 5.85497 10.942 6.04579 10.9139 6.17673C10.8145 6.64071 10.2793 6.66389 10 6.66389C9.72074 6.66389 9.18461 6.63639 9.08607 6.17673C9.058 6.0458 9.01357 5.85497 8.94951 5.62445C8.8205 5.16002 8.61667 4.55125 8.3168 3.95144C7.69259 2.70312 6.80983 1.77704 5.5574 1.77704C4.11783 1.77704 3.06695 2.47865 2.43762 3.55751ZM10 3.34981C10.0303 3.2856 10.0616 3.22119 10.0938 3.15672C10.8024 1.73947 12.141 0 14.4426 0C16.5571 0 18.1718 1.07542 19.0974 2.66212C20.0075 4.22234 20.2396 6.2427 19.7471 8.21213C19.5139 9.14525 18.9166 10.1142 18.2116 11.0198C17.4939 11.9419 16.6041 12.8738 15.6985 13.7369C13.8866 15.4639 11.9509 16.9726 11.0493 17.6533C10.4268 18.1235 9.57325 18.1235 8.95066 17.6533C8.04911 16.9726 6.11348 15.4639 4.30148 13.7369C3.39594 12.8738 2.50612 11.9419 1.78837 11.0198C1.08338 10.1142 0.486107 9.14525 0.252836 8.21213C-0.239533 6.2427 -0.00747901 4.22235 0.902653 2.66212C1.82822 1.07542 3.44289 0 5.5574 0C7.85901 0 9.19758 1.73947 9.90618 3.15671C9.93843 3.22119 9.96971 3.2856 10 3.34981Z\" fill=\"white\"></path>\n                    </svg>\n                ");
      Theme.showSuccess($currentTarget.data('remove-message'));
    } else {
      wishlistArray.push(id);
      $currentTarget.addClass('x-favorite--active').html("\n                    <svg class=\"x-favorite_icon x-favorite-filled\" width=\"20\" height=\"18\" viewBox=\"0 0 20 18\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path d=\"M2 3.5L5.5 0.5L10 4.5L14 0.5L18.5 3L19.5 5L18 9.5L15 13L14 14.5L10 17.5L6.5 14L3.5 12L1 8.5L2 3.5Z\" fill=\"#FF0000\"></path>\n                        <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M2.43762 3.55751C1.79286 4.66283 1.58065 6.19655 1.97681 7.7812C2.11937 8.35136 2.53381 9.08448 3.19064 9.9283C3.83472 10.7557 4.65681 11.6207 5.52752 12.4505C7.25399 14.0962 9.11202 15.5477 10 16.219C10.888 15.5477 12.746 14.0962 14.4724 12.4505C15.3432 11.6207 16.1653 10.7557 16.8093 9.9283C17.4662 9.08448 17.8806 8.35136 18.0232 7.7812C18.4193 6.19655 18.2072 4.66284 17.5624 3.55751C16.933 2.47865 15.8822 1.77704 14.4426 1.77704C13.1901 1.77704 12.3074 2.70312 11.6832 3.95143C11.3833 4.55124 11.1795 5.16002 11.0505 5.62445C10.9864 5.85497 10.942 6.04579 10.9139 6.17673C10.8145 6.64071 10.2793 6.66389 10 6.66389C9.72074 6.66389 9.18461 6.63639 9.08607 6.17673C9.058 6.0458 9.01357 5.85497 8.94951 5.62445C8.8205 5.16002 8.61667 4.55125 8.3168 3.95144C7.69259 2.70312 6.80983 1.77704 5.5574 1.77704C4.11783 1.77704 3.06695 2.47865 2.43762 3.55751ZM10 3.34981C10.0303 3.2856 10.0616 3.22119 10.0938 3.15672C10.8024 1.73947 12.141 0 14.4426 0C16.5571 0 18.1718 1.07542 19.0974 2.66212C20.0075 4.22234 20.2396 6.2427 19.7471 8.21213C19.5139 9.14525 18.9166 10.1142 18.2116 11.0198C17.4939 11.9419 16.6041 12.8738 15.6985 13.7369C13.8866 15.4639 11.9509 16.9726 11.0493 17.6533C10.4268 18.1235 9.57325 18.1235 8.95066 17.6533C8.04911 16.9726 6.11348 15.4639 4.30148 13.7369C3.39594 12.8738 2.50612 11.9419 1.78837 11.0198C1.08338 10.1142 0.486107 9.14525 0.252836 8.21213C-0.239533 6.2427 -0.00747901 4.22235 0.902653 2.66212C1.82822 1.07542 3.44289 0 5.5574 0C7.85901 0 9.19758 1.73947 9.90618 3.15671C9.93843 3.22119 9.96971 3.2856 10 3.34981Z\" fill=\"#FF0000\"></path>\n                    </svg>\n                ");
      Theme.showSuccess($currentTarget.data('add-message'));
    }
    setCookie(cookieName, wishlistArray.join(','), 365);
    initWishlistCount();
  }).on('click', '[data-bb-toggle="toggle-filter-offcanvas"]', function (e) {
    e.preventDefault();
    $('.search-box-offcanvas').toggleClass('active');
  }).on('click', '.search-box-offcanvas-backdrop', function (e) {
    $('.search-box-offcanvas').removeClass('active');
  });
  $("[data-bb-toggle=\"change-search-type\"][data-value=\"".concat($('.flat-tab').find('form input[name="type"]'), "\"]")).trigger('click');
  document.addEventListener('shortcode.loaded', function (e) {
    var _e$detail = e.detail,
      name = _e$detail.name,
      html = _e$detail.html,
      attributes = _e$detail.attributes;
    switch (name) {
      case 'image-slider':
        initImageSlider();
        break;
      case 'testimonials':
        initTestimonials();
        break;
      case 'location':
        initLocation();
        break;
      case 'properties':
        initWishlist();
        if (attributes.style === '2') {
          initPropertiesTab();
        }
        if (attributes.style === '7') {
          initProperties();
        }
        break;
      case 'property-categories':
        initPropertyCategories();
        break;
      case 'services':
        initServices();
        break;
    }
  });
  if ($("[data-countdown]").length > 0) {
    var $element = $("[data-countdown]");
    $element.countdown($element.data('date'), function (event) {
      $element.find('[data-days]').text(event.strftime('%D'));
      $element.find('[data-hours]').text(event.strftime('%H'));
      $element.find('[data-minutes]').text(event.strftime('%M'));
      $element.find('[data-seconds]').text(event.strftime('%S'));
    });
  }
});
/******/ })()
;
//# sourceMappingURL=script.js.map