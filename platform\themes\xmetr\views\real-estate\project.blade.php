@php
    Theme::set('breadcrumbEnabled', 'yes');

    Theme::asset()->usePath()->add('fancybox', 'plugins/fancybox/jquery.fancybox.min.css');
    Theme::asset()->container('footer')->usePath()->add('fancybox', 'plugins/fancybox/jquery.fancybox.min.js');
    Theme::asset()->usePath()->add('leaflet', 'plugins/leaflet/leaflet.css');
    Theme::asset()->container('footer')->usePath()->add('leaflet', 'plugins/leaflet/leaflet.js');
    Theme::layout('full-width');
@endphp

<div class="wrapper ovh pt-[20px] pb-[40px]">
    <div class="body_content container flex flex-col gap-[25px]">

        <div class="flex gap-[20px] items-stretch max-[768px]:flex-col">
            <!-- Large image on the left -->
            @foreach($project->images as $image)
                @if($loop->first)
                    <div class="aspect-[16/10] flex-[3] relative rounded-[10px] overflow-hidden">
                        <div class="w-full h-full">
                            <a href="{{ RvMedia::getImageUrl($image) }}" data-fancybox="gallery">
                                {!! RvMedia::image($image, $project->name, 'full', false, ['class' => 'w-full h-full']) !!}
                            </a>
                        </div>

                        <!-- Badges -->
                        <div class="flex gap-[5px] flex-wrap items-start absolute top-[20px] left-[20px]">
                            <a href="{{ $project->city->url }}" class="p-[15px] bg-[#FFFFFF]/[.80] rounded-[30px] w-[50px] h-[50px] absolute top-[20px] left-[20px]">
                                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M15.8337 10H4.16699" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M10 15.8332L4.16667 9.99984L10 4.1665" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </a>
                            @if($project->is_featured)
                            <div class="rounded-[5px] bg-[#E45E45] px-[10px] py-[5px]">
                            <p class="text-white text-[13px] font-bold">⚡ {{ __('Featured') }}</p>
                            </div>
                            @endif
                        </div>

                        <!-- Favorite (Add .x-favorite--active) to activate favorite button -->
                        @if (RealEstateHelper::isEnabledWishlist())

                        <button type="button" class="x-favorite p-[15px] bg-[#5E2DC2]/[.15] rounded-[30px] w-[50px] h-[50px] absolute top-[20px] right-[20px]"
                                data-type="project"
                                data-bb-toggle="add-to-wishlist"
                                data-id="{{ $project->getKey() }}"
                                data-add-message="{{ __('Added ":name" to wishlist successfully!', ['name' => $project->name]) }}"
                                data-remove-message="{{ __('Removed ":name" from wishlist successfully!', ['name' => $project->name]) }}"
                        >
                        <svg class="x-favorite_icon x-favorite-notFilled" width="20" height="18" viewBox="0 0 20 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M2.43762 3.55751C1.79286 4.66283 1.58065 6.19655 1.97681 7.7812C2.11937 8.35136 2.53381 9.08448 3.19064 9.9283C3.83472 10.7557 4.65681 11.6207 5.52752 12.4505C7.25399 14.0962 9.11202 15.5477 10 16.219C10.888 15.5477 12.746 14.0962 14.4724 12.4505C15.3432 11.6207 16.1653 10.7557 16.8093 9.9283C17.4662 9.08448 17.8806 8.35136 18.0232 7.7812C18.4193 6.19655 18.2072 4.66284 17.5624 3.55751C16.933 2.47865 15.8822 1.77704 14.4426 1.77704C13.1901 1.77704 12.3074 2.70312 11.6832 3.95143C11.3833 4.55124 11.1795 5.16002 11.0505 5.62445C10.9864 5.85497 10.942 6.04579 10.9139 6.17673C10.8145 6.64071 10.2793 6.66389 10 6.66389C9.72074 6.66389 9.18461 6.63639 9.08607 6.17673C9.058 6.0458 9.01357 5.85497 8.94951 5.62445C8.8205 5.16002 8.61667 4.55125 8.3168 3.95144C7.69259 2.70312 6.80983 1.77704 5.5574 1.77704C4.11783 1.77704 3.06695 2.47865 2.43762 3.55751ZM10 3.34981C10.0303 3.2856 10.0616 3.22119 10.0938 3.15672C10.8024 1.73947 12.141 0 14.4426 0C16.5571 0 18.1718 1.07542 19.0974 2.66212C20.0075 4.22234 20.2396 6.2427 19.7471 8.21213C19.5139 9.14525 18.9166 10.1142 18.2116 11.0198C17.4939 11.9419 16.6041 12.8738 15.6985 13.7369C13.8866 15.4639 11.9509 16.9726 11.0493 17.6533C10.4268 18.1235 9.57325 18.1235 8.95066 17.6533C8.04911 16.9726 6.11348 15.4639 4.30148 13.7369C3.39594 12.8738 2.50612 11.9419 1.78837 11.0198C1.08338 10.1142 0.486107 9.14525 0.252836 8.21213C-0.239533 6.2427 -0.00747901 4.22235 0.902653 2.66212C1.82822 1.07542 3.44289 0 5.5574 0C7.85901 0 9.19758 1.73947 9.90618 3.15671C9.93843 3.22119 9.96971 3.2856 10 3.34981Z" fill="white"></path>
                        </svg>
                        <svg class="x-favorite_icon x-favorite-filled" width="20" height="18" viewBox="0 0 20 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M2 3.5L5.5 0.5L10 4.5L14 0.5L18.5 3L19.5 5L18 9.5L15 13L14 14.5L10 17.5L6.5 14L3.5 12L1 8.5L2 3.5Z" fill="#FF0000"></path>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M2.43762 3.55751C1.79286 4.66283 1.58065 6.19655 1.97681 7.7812C2.11937 8.35136 2.53381 9.08448 3.19064 9.9283C3.83472 10.7557 4.65681 11.6207 5.52752 12.4505C7.25399 14.0962 9.11202 15.5477 10 16.219C10.888 15.5477 12.746 14.0962 14.4724 12.4505C15.3432 11.6207 16.1653 10.7557 16.8093 9.9283C17.4662 9.08448 17.8806 8.35136 18.0232 7.7812C18.4193 6.19655 18.2072 4.66284 17.5624 3.55751C16.933 2.47865 15.8822 1.77704 14.4426 1.77704C13.1901 1.77704 12.3074 2.70312 11.6832 3.95143C11.3833 4.55124 11.1795 5.16002 11.0505 5.62445C10.9864 5.85497 10.942 6.04579 10.9139 6.17673C10.8145 6.64071 10.2793 6.66389 10 6.66389C9.72074 6.66389 9.18461 6.63639 9.08607 6.17673C9.058 6.0458 9.01357 5.85497 8.94951 5.62445C8.8205 5.16002 8.61667 4.55125 8.3168 3.95144C7.69259 2.70312 6.80983 1.77704 5.5574 1.77704C4.11783 1.77704 3.06695 2.47865 2.43762 3.55751ZM10 3.34981C10.0303 3.2856 10.0616 3.22119 10.0938 3.15672C10.8024 1.73947 12.141 0 14.4426 0C16.5571 0 18.1718 1.07542 19.0974 2.66212C20.0075 4.22234 20.2396 6.2427 19.7471 8.21213C19.5139 9.14525 18.9166 10.1142 18.2116 11.0198C17.4939 11.9419 16.6041 12.8738 15.6985 13.7369C13.8866 15.4639 11.9509 16.9726 11.0493 17.6533C10.4268 18.1235 9.57325 18.1235 8.95066 17.6533C8.04911 16.9726 6.11348 15.4639 4.30148 13.7369C3.39594 12.8738 2.50612 11.9419 1.78837 11.0198C1.08338 10.1142 0.486107 9.14525 0.252836 8.21213C-0.239533 6.2427 -0.00747901 4.22235 0.902653 2.66212C1.82822 1.07542 3.44289 0 5.5574 0C7.85901 0 9.19758 1.73947 9.90618 3.15671C9.93843 3.22119 9.96971 3.2856 10 3.34981Z" fill="#FF0000"></path>
                        </svg>
                        </button>

                        {{-- <button type="button" class="x-favorite p-[15px] bg-[#5E2DC2]/[.15] rounded-[30px] w-[50px] h-[50px] relative"
                                data-type="property"
                                data-bb-toggle="add-to-wishlist"
                                data-id="{{ $project->getKey() }}"
                                data-add-message="{{ __('Added ":name" to wishlist successfully!', ['name' => $project->name]) }}"
                                data-remove-message="{{ __('Removed ":name" from wishlist successfully!', ['name' => $project->name]) }}"
                        >

                            <svg class="x-favorite_icon x-favorite-notFilled" width="20" height="18" viewBox="0 0 20 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M2.43762 3.55751C1.79286 4.66283 1.58065 6.19655 1.97681 7.7812C2.11937 8.35136 2.53381 9.08448 3.19064 9.9283C3.83472 10.7557 4.65681 11.6207 5.52752 12.4505C7.25399 14.0962 9.11202 15.5477 10 16.219C10.888 15.5477 12.746 14.0962 14.4724 12.4505C15.3432 11.6207 16.1653 10.7557 16.8093 9.9283C17.4662 9.08448 17.8806 8.35136 18.0232 7.7812C18.4193 6.19655 18.2072 4.66284 17.5624 3.55751C16.933 2.47865 15.8822 1.77704 14.4426 1.77704C13.1901 1.77704 12.3074 2.70312 11.6832 3.95143C11.3833 4.55124 11.1795 5.16002 11.0505 5.62445C10.9864 5.85497 10.942 6.04579 10.9139 6.17673C10.8145 6.64071 10.2793 6.66389 10 6.66389C9.72074 6.66389 9.18461 6.63639 9.08607 6.17673C9.058 6.0458 9.01357 5.85497 8.94951 5.62445C8.8205 5.16002 8.61667 4.55125 8.3168 3.95144C7.69259 2.70312 6.80983 1.77704 5.5574 1.77704C4.11783 1.77704 3.06695 2.47865 2.43762 3.55751ZM10 3.34981C10.0303 3.2856 10.0616 3.22119 10.0938 3.15672C10.8024 1.73947 12.141 0 14.4426 0C16.5571 0 18.1718 1.07542 19.0974 2.66212C20.0075 4.22234 20.2396 6.2427 19.7471 8.21213C19.5139 9.14525 18.9166 10.1142 18.2116 11.0198C17.4939 11.9419 16.6041 12.8738 15.6985 13.7369C13.8866 15.4639 11.9509 16.9726 11.0493 17.6533C10.4268 18.1235 9.57325 18.1235 8.95066 17.6533C8.04911 16.9726 6.11348 15.4639 4.30148 13.7369C3.39594 12.8738 2.50612 11.9419 1.78837 11.0198C1.08338 10.1142 0.486107 9.14525 0.252836 8.21213C-0.239533 6.2427 -0.00747901 4.22235 0.902653 2.66212C1.82822 1.07542 3.44289 0 5.5574 0C7.85901 0 9.19758 1.73947 9.90618 3.15671C9.93843 3.22119 9.96971 3.2856 10 3.34981Z" fill="white"></path>
                            </svg>
                            <svg class="x-favorite_icon x-favorite-filled" width="20" height="18" viewBox="0 0 20 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M2 3.5L5.5 0.5L10 4.5L14 0.5L18.5 3L19.5 5L18 9.5L15 13L14 14.5L10 17.5L6.5 14L3.5 12L1 8.5L2 3.5Z" fill="#FF0000"></path>
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M2.43762 3.55751C1.79286 4.66283 1.58065 6.19655 1.97681 7.7812C2.11937 8.35136 2.53381 9.08448 3.19064 9.9283C3.83472 10.7557 4.65681 11.6207 5.52752 12.4505C7.25399 14.0962 9.11202 15.5477 10 16.219C10.888 15.5477 12.746 14.0962 14.4724 12.4505C15.3432 11.6207 16.1653 10.7557 16.8093 9.9283C17.4662 9.08448 17.8806 8.35136 18.0232 7.7812C18.4193 6.19655 18.2072 4.66284 17.5624 3.55751C16.933 2.47865 15.8822 1.77704 14.4426 1.77704C13.1901 1.77704 12.3074 2.70312 11.6832 3.95143C11.3833 4.55124 11.1795 5.16002 11.0505 5.62445C10.9864 5.85497 10.942 6.04579 10.9139 6.17673C10.8145 6.64071 10.2793 6.66389 10 6.66389C9.72074 6.66389 9.18461 6.63639 9.08607 6.17673C9.058 6.0458 9.01357 5.85497 8.94951 5.62445C8.8205 5.16002 8.61667 4.55125 8.3168 3.95144C7.69259 2.70312 6.80983 1.77704 5.5574 1.77704C4.11783 1.77704 3.06695 2.47865 2.43762 3.55751ZM10 3.34981C10.0303 3.2856 10.0616 3.22119 10.0938 3.15672C10.8024 1.73947 12.141 0 14.4426 0C16.5571 0 18.1718 1.07542 19.0974 2.66212C20.0075 4.22234 20.2396 6.2427 19.7471 8.21213C19.5139 9.14525 18.9166 10.1142 18.2116 11.0198C17.4939 11.9419 16.6041 12.8738 15.6985 13.7369C13.8866 15.4639 11.9509 16.9726 11.0493 17.6533C10.4268 18.1235 9.57325 18.1235 8.95066 17.6533C8.04911 16.9726 6.11348 15.4639 4.30148 13.7369C3.39594 12.8738 2.50612 11.9419 1.78837 11.0198C1.08338 10.1142 0.486107 9.14525 0.252836 8.21213C-0.239533 6.2427 -0.00747901 4.22235 0.902653 2.66212C1.82822 1.07542 3.44289 0 5.5574 0C7.85901 0 9.19758 1.73947 9.90618 3.15671C9.93843 3.22119 9.96971 3.2856 10 3.34981Z" fill="#FF0000"></path>
                            </svg>
                        </button> --}}
                        @endif

                        <!-- Gradient overlay -->
                        <div class="absolute bottom-0 left-0 w-full h-[95px] rounded-b-[10px] pointer-events-none touch-none" style="background: linear-gradient(180deg, rgba(33, 35, 41, 0), rgba(33, 35, 41, .8));"></div>
                    </div>
                @endif
            @endforeach

            <!-- Smaller images on the right -->
            <div class="flex flex-col gap-[12px] flex-1 max-[768px]:flex-row">
                @foreach($project->images as $image)
                    @if(!$loop->first && $loop->iteration <= 3)
                        <div class="aspect-[16/10] flex">
                            <a href="{{ RvMedia::getImageUrl($image) }}" class="w-full h-full rounded-[10px] overflow-hidden popup-img" data-fancybox="gallery">
                                {!! RvMedia::image($image, $project->name, 'medium-rectangle', false, ['class' => 'w-full h-full']) !!}
                            </a>
                        </div>
                    @endif
                    @if($loop->iteration == 4)
                    <div class="aspect-[16/10] flex relative justify-center items-end">
                        <a href="{{ RvMedia::getImageUrl($image) }}" class="w-full h-full rounded-[10px] overflow-hidden popup-img" data-fancybox="gallery">
                            {!! RvMedia::image($image, $project->name, 'medium-rectangle', false, ['class' => 'w-full h-full']) !!}
                        </a>

                        <div class="px-[12px] py-[10px] rounded-[5px] bg-white absolute bottom-[20px] z-[2]" style="box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.25);">
                            <a href="{{ RvMedia::getImageUrl($project->images[3]) }}" data-fancybox="gallery" class="text-[15px] text-black font-bold popup-img">📸 {{ __(':count photo', ['count' => count($project->images)]) }}</a>
                        </div>
                    </div>
                    @endif
                @endforeach

                <!-- "View All Photos" button -->

                @foreach($project->images as $image)

                @if(!$loop->first && $loop->iteration > 4)
                <a href="{{ RvMedia::getImageUrl($image) }}" data-fancybox="gallery" @style(['display: none' => $loop->iteration > 4])>
                    {!! RvMedia::image($image, $project->name, 'medium-rectangle', false, ['class' => 'w-full h-full']) !!}
                </a>
                @endif
                @endforeach
            </div>
        </div>

         <div class="flex justify-between items-start gap-[24px] max-[1280px]:flex-col">
            <div class="flex flex-col gap-[25px]">

                <div class="flex items-center gap-[20px] flex-wrap">
                    <p class="text-[13px] text-black">🕒 {{ Theme::formatDate($project->created_at) }}</p>
                    @if (setting('real_estate_display_views_count_in_detail_page', true))
                    <p class="text-[13px] text-black">
                        👁️ {{ $project->views_text }}
                    </p>
                    @endif
                </div>

                <h3 class="title">{{ $project->name }}</h3>

                {{-- Description --}}
                <div class="grid grid-cols-3 gap-y-[20px] gap-x-[16px] max-[1024px]:grid-cols-2">

                    @if ($project->number_block)
                    <div class="flex items-center gap-[10px]">
                        <div class="w-[50px] h-[50px] bg-white rounded-[10px] border border-[#D6D6D7] flex justify-center items-center">
                            <p class="text-[25px]"> 🏢</p>
                        </div>

                        <div class="flex flex-col">
                            <p class="text-black text-[15px] font-bold">{{ __('Blocks:') }}</p>
                            <p class="text-black text-[15px]">{{ $project->number_block }}</p>
                        </div>
                    </div>
                    @endif

                    @if ($project->number_floor)
                    <div class="flex items-center gap-[10px]">
                        <div class="w-[50px] h-[50px] bg-white rounded-[10px] border border-[#D6D6D7] flex justify-center items-center">
                            <p class="text-[25px]">🚪</p>
                        </div>

                        <div class="flex flex-col">
                            <p class="text-black text-[15px] font-bold">{{ __('Floors:') }}</p>
                            <p class="text-black text-[15px]">{{ number_format($project->number_floor) }}</p>
                        </div>
                    </div>
                    @endif

                    @if ($project->number_flat)
                    <div class="flex items-center gap-[10px]">
                        <div class="w-[50px] h-[50px] bg-white rounded-[10px] border border-[#D6D6D7] flex justify-center items-center">
                            <p class="text-[25px]"> 🏠</p>
                        </div>

                        <div class="flex flex-col">
                            <p class="text-black text-[15px] font-bold">{{ __('Flats:') }}</p>
                            <p class="text-black text-[15px]">{{ number_format($project->number_flat) }}</p>
                        </div>
                    </div>
                    @endif
                    @if ($project->year_built)
                    <div class="flex items-center gap-[10px]">
                        <div class="w-[50px] h-[50px] bg-white rounded-[10px] border border-[#D6D6D7] flex justify-center items-center">
                            <p class="text-[25px]"> 👷🏾</p>
                        </div>

                        <div class="flex flex-col">
                            <p class="text-black text-[15px] font-bold">{{ __('Built:') }}</p>
                            <p class="text-black text-[15px]">{{ $project->year_built }}</p>
                        </div>
                    </div>
                    @endif
                    @if ($project->parking)
                    <div class="flex items-center gap-[10px]">
                        <div class="w-[50px] h-[50px] bg-white rounded-[10px] border border-[#D6D6D7] flex justify-center items-center">
                            <p class="text-[25px]"> 🚗</p>
                        </div>

                        <div class="flex flex-col">
                            <p class="text-black text-[15px] font-bold">{{ __('Parking:') }}</p>
                            <p class="text-black text-[15px]">{{ number_format($project->parking) }}</p>
                        </div>
                    </div>
                    @endif
                    @if ($project->build_class && $project->build_class->getValue() !== null)
                    <div class="flex items-center gap-[10px]">
                        <div class="w-[50px] h-[50px] bg-white rounded-[10px] border border-[#D6D6D7] flex justify-center items-center">
                            <p class="text-[25px]"> ✨</p>
                        </div>

                        <div class="flex flex-col">
                            <p class="text-black text-[15px] font-bold">{{ __('Build Class:') }}</p>
                            <p class="text-black text-[15px]">{{ $project->build_class->label() }}</p>
                        </div>
                    </div>
                    @endif

                </div>

                <span class="w-full h-[1px] block bg-[#D6D6D7]"></span>

                @if ($project->content)
                    <h4 class="title">{{ __('Description') }}</h4>
                    <div class="text-black text-[15px]">
                        {!! BaseHelper::clean($project->content) !!}
                    </div>
                    <span class="w-full h-[1px] block bg-[#D6D6D7]"></span>
                @endif



                {{-- Features --}}
                @if ($project->features->isNotEmpty())
                    <h4 class="title">{{ __('Amenities') }}</h4>
                    <div class="flex items-center gap-[5px] flex-wrap">
                        @foreach ($project->features as $feature)
                        {{-- @if($feature->icon)
                            {!! BaseHelper::renderIcon($feature->icon) !!}
                        @endif --}}
                        <div class="x-badge" data-bs-toggle="tooltip" title="{{ $feature->name }}">{{ $feature->name }}</div>
                        @endforeach
                    </div>
                    <span class="w-full h-[1px] block bg-[#D6D6D7]"></span>
                @endif

                @include(Theme::getThemeNamespace('views.real-estate.single-layouts.partials.district-ratings'), ['class' => 'single-property-element', 'model' => $property])


                <div class="single-property-element single-property-map">
                    <div class="h7 title fw-7">{{ __('Location') }}</div>
                    @if (theme_option('real_estate_show_map_on_single_detail_page', 'yes') === 'yes')
                        @if ($project->latitude && $project->longitude)
                            <div data-bb-toggle="detail-map" id="map" style="min-height: 400px;" data-tile-layer="{{ RealEstateHelper::getMapTileLayer() }}" data-center="{{ json_encode([$project->latitude, $project->longitude]) }}" data-map-icon="{{ $project->map_icon }}"></div>
                        @else
                            <iframe width="100%" style="min-height: 400px" src="https://maps.google.com/maps?q={{ urlencode($project->location) }}%20&t=&z=13&ie=UTF8&iwloc=&output=embed" frameborder="0" scrolling="no" marginheight="0" marginwidth="0"></iframe>
                        @endif
                    @endif
                    <ul class="info-map">
                        <li>
                            <div class="fw-7">{{ __('Address') }}</div>
                            <a class="mt-4 text-variant-1" href="https://www.google.com/maps/search/{{ urlencode($project->location) }}">
                                {{ $project->location ?: $project->short_address }}
                            </a>
                        </li>
                    </ul>

                    @include(Theme::getThemeNamespace('views.real-estate.partials.social-sharing'), ['model' => $project])
                </div>

                @include(Theme::getThemeNamespace('views.real-estate.single-layouts.partials.reviews'), ['model' => $project])

            </div>
            {{-- Account --}}
            <div class="agent w-[440px] shrink-0 max-[1280px]:w-full min-[1280px]:pt-[88px]">
                <div class="flex flex-col gap-[20px] items-center">
                    @if (! RealEstateHelper::hideAgentInfoInPropertyDetailPage() && ($account = $project->author))
                    <div class="px-[20px] py-[30px] bg-[#F7F7F7] rounded-[10px] flex flex-col gap-[30px] max-[1280px]:w-full" id="apartmentsContacts" style="box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.25);">
                    <div class="flex items-center gap-[20px]">
                    {{-- <img src="./images/avatars/1.png" alt="Avatar" class="object-cover shrink-0 rounded-[32px]" width="100px" height="100px"> --}}
                    {{ RvMedia::image($account->avatar->url ?: $account->avatar_url, $account->name, 'thumb', false, ['class'=> 'object-cover shrink-0 rounded-[32px]', 'width'=> '100px', 'height'=>'100px']) }}

                    <div class="w-full flex flex-col gap-[2px]">
                        <div class="flex flex-col gap-[5px]">
                        <p class="text-black text-[15px] font-bold text-ellipsis line-clamp-1">{{ $account->name }}</p>

                        <div class="flex items-center gap-[10px]">
                            @if ($account->is_verified)
                            <svg width="20" height="20" class="max-[430px]:w-[16px] max-[430px]:h-[16px]" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M19.5562 8.74032L18.1961 7.16071C17.9361 6.86078 17.7261 6.30092 17.7261 5.90102V4.20145C17.7261 3.14171 16.8561 2.27193 15.796 2.27193H14.0959C13.7059 2.27193 13.1358 2.06198 12.8358 1.80205L11.2557 0.442389C10.5657 -0.147463 9.43561 -0.147463 8.73556 0.442389L7.16543 1.81205C6.86541 2.06198 6.29538 2.27193 5.90535 2.27193H4.17525C3.11519 2.27193 2.24514 3.14171 2.24514 4.20145V5.91102C2.24514 6.30092 2.03512 6.86078 1.78511 7.16071L0.435026 8.75032C-0.145009 9.44015 -0.145009 10.5599 0.435026 11.2497L1.78511 12.8393C2.03512 13.1392 2.24514 13.6991 2.24514 14.089V15.7986C2.24514 16.8583 3.11519 17.7281 4.17525 17.7281H5.90535C6.29538 17.7281 6.86541 17.938 7.16543 18.198L8.74557 19.5576C9.43561 20.1475 10.5657 20.1475 11.2657 19.5576L12.8458 18.198C13.1458 17.938 13.7059 17.7281 14.1059 17.7281H15.806C16.8661 17.7281 17.7361 16.8583 17.7361 15.7986V14.099C17.7361 13.7091 17.9461 13.1392 18.2061 12.8393L19.5662 11.2597C20.1463 10.5699 20.1462 9.43015 19.5562 8.74032ZM14.1559 8.11048L9.3256 12.9393C9.18559 13.0792 8.99558 13.1592 8.79557 13.1592C8.59556 13.1592 8.40554 13.0792 8.26554 12.9393L5.84535 10.5199C5.55533 10.2299 5.55533 9.75007 5.84535 9.46014C6.13537 9.17021 6.6154 9.17021 6.90542 9.46014L8.79557 11.3497L13.0958 7.05073C13.3858 6.76081 13.8659 6.76081 14.1559 7.05073C14.4459 7.34066 14.4459 7.82054 14.1559 8.11048Z" fill="#0071FF" />
                            </svg>
                            @endif

                            <p class="text-[15px] text-[#717171] max-[430px]:text-[13px]">{{ $account->with_xmetr }}</p>
                        </div>
                        @if($account->spokenLanguages->isNotEmpty())
                        <div class="w-full flex items-start gap-[8px]">
                            <p class="shrink-0 text-[13px] text-[#717171]">{{ __('Host Speak') }}</p>
                            @foreach($account->spokenLanguages as $language)
                            <div class="flag-container flex items-center gap-[8px]">
                                <span class="language-flag">{!! language_flag($language->flag, $language->name, 20) !!}</span>
                            </div>
                            @endforeach
                        </div>
                        @endif
                        </div>
                        <p class="text-[15px] text-[#717171] max-[430px]:text-[13px]">{{ $account->account_type->label() }}</p>
                        {{-- <a href="{{ $account->url }}" class="text-black text-[15px] underline">{{ $account->properties_count }} {{ __('listing') }}</a> --}}
                    </div>
                    </div>


                    <div class="flex items-center gap-[10px]">
                        @if ($whatsapp = $account->getMetaData('whatsapp', true))
                    <a href="@if (auth('account')->check()) {{ route('public.project.contact.click', ['project' => $project->id, 'type' => 'whatsapp']) }} @else #modalSignin @endif"  @if (auth('account')->check()) target="_blank"  @else  role="button"  data-bs-toggle="modal"  @endif class="h-[50px] rounded-[10px] bg-[#7DC678] flex justify-center items-center gap-[8px] px-[33px] w-full max-[500px]:px-[8px]">
                        <svg width="20" height="20" class="max-[430px]:w-[16px] max-[430px]:h-[16px]" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M4.40471 6.15476C4.5051 5.36085 5.53688 4.28185 6.35572 4.40158L6.35441 4.40027C7.1513 4.5517 7.78212 5.92327 8.13783 6.54C8.38986 6.98747 8.2262 7.44084 7.99112 7.63214C7.67399 7.88775 7.17807 8.24043 7.28212 8.59455C7.4671 9.22407 9.66331 11.4203 10.7435 12.0446C11.1523 12.2809 11.4488 11.6576 11.702 11.3383C11.886 11.0927 12.3397 10.9459 12.7861 11.1884C13.4529 11.5766 14.081 12.0279 14.6619 12.5359C14.952 12.7784 15.02 13.1367 14.819 13.5155C14.465 14.1825 13.4411 15.0621 12.6978 14.8833C11.3995 14.5712 6.14938 12.5359 4.46298 6.63958C4.36814 6.36064 4.39202 6.25518 4.40471 6.15476Z" fill="white" />
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M9.66331 19.3266C8.5884 19.3266 7.99367 19.2113 7.02786 18.8874L5.17816 19.8122C4.00994 20.3964 2.63545 19.5469 2.63545 18.2407V16.2519C0.74368 14.4879 0 12.454 0 9.66331C0 4.32641 4.32641 0 9.66331 0C15.0002 0 19.3266 4.32641 19.3266 9.66331C19.3266 15.0002 15.0002 19.3266 9.66331 19.3266ZM4.39241 15.4879L3.83365 14.9669C2.36388 13.5965 1.75697 12.0643 1.75697 9.66331C1.75697 5.29676 5.29676 1.75697 9.66331 1.75697C14.0299 1.75697 17.5697 5.29676 17.5697 9.66331C17.5697 14.0299 14.0299 17.5697 9.66331 17.5697C8.79739 17.5697 8.39127 17.4915 7.58653 17.2216L6.89475 16.9896L4.39241 18.2407V15.4879Z" fill="white" />
                        </svg>

                        <p class="text-white text-[15px] font-bold">{{ __('WhatsApp') }}</p>
                    </a>
                    @endif
                    @if ($telegram = $account->getMetaData('telegram', true))
                    <a href="@if (auth('account')->check()) {{ route('public.project.contact.click', ['project' => $project->id, 'type' => 'telegram']) }} @else #modalSignin @endif" @if (auth('account')->check()) target="_blank"  @else  role="button"  data-bs-toggle="modal"  @endif class="h-[50px] rounded-[10px] bg-[#4DA0D9] flex justify-center items-center gap-[8px] px-[33px] w-full max-[500px]:px-[8px]">
                        <svg width="20" height="20" class="max-[430px]:w-[16px] max-[430px]:h-[16px]" viewBox="0 0 20 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M19.9615 2.17749C20.2493 0.774406 18.8712 -0.391584 17.5352 0.124619L1.15816 6.45211C-0.333844 7.02856 -0.400324 9.11491 1.05194 9.78514L4.6184 11.4312L6.31501 17.3693C6.40314 17.6778 6.6479 17.9166 6.95843 17.9971C7.26895 18.0775 7.59888 17.9877 7.82571 17.7608L10.4392 15.1473L14.1001 17.8931C15.1628 18.6901 16.6934 18.1096 16.9603 16.8083L19.9615 2.17749ZM1.81073 8.14112L18.1878 1.81364L15.1865 16.4445L10.8974 13.2276C10.537 12.9573 10.0327 12.9932 9.71406 13.3117L8.59469 14.4311L8.93103 12.5814L15.5212 5.99131C15.8419 5.67059 15.8758 5.16203 15.6005 4.80159C15.3251 4.44113 14.8257 4.34003 14.4318 4.56507L5.33067 9.76568L1.81073 8.14112ZM6.44037 11.217L6.98935 13.1386L7.20013 11.9793C7.23307 11.7981 7.32048 11.6312 7.4507 11.5011L9.46048 9.49136L6.44037 11.217Z" fill="white" />
                        </svg>

                        <p class="text-white text-[15px] font-bold">{{ __('Telegram') }}</p>
                    </a>
                    @endif

                    @if ($account->phone)

                    <a href="@if (auth('account')->check()) {{ route('public.project.contact.click', ['project' => $project->id, 'type' => 'phone']) }} @else #modalSignin @endif"  @if (auth('account')->check()) target="_blank"  @else  role="button"  data-bs-toggle="modal"  @endif class="w-[50px] h-[50px] shrink-0 rounded-[10px] bg-[#212329] hover:bg-[#3e424d] flex justify-center items-center gap-[8px] px-[8px]">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M15.6202 8.75169C15.1902 8.75169 14.8501 8.40168 14.8501 7.98164C14.8501 7.61164 14.4801 6.84163 13.8601 6.17162C13.2501 5.52162 12.5801 5.14161 12.0201 5.14161C11.5901 5.14161 11.2501 4.79161 11.2501 4.3716C11.2501 3.9516 11.6001 3.6016 12.0201 3.6016C13.0201 3.6016 14.0701 4.1416 14.9901 5.11161C15.8502 6.02162 16.4002 7.15163 16.4002 7.97164C16.4002 8.40168 16.0502 8.75169 15.6202 8.75169Z" fill="white" />
                        <path d="M19.23 8.75009C18.8 8.75009 18.46 8.40008 18.46 7.98008C18.46 4.43004 15.57 1.55002 12.0299 1.55002C11.5999 1.55002 11.2599 1.20001 11.2599 0.780008C11.2599 0.360004 11.5999 0 12.0199 0C16.42 0 20 3.58003 20 7.98008C20 8.40008 19.65 8.75009 19.23 8.75009Z" fill="white" />
                        <path d="M9.05009 12.9501L7.20007 14.8001C6.81007 15.1902 6.19006 15.1902 5.79006 14.8101C5.68006 14.7001 5.57006 14.6001 5.46005 14.4901C4.43004 13.4501 3.50004 12.3601 2.67003 11.2201C1.85002 10.0801 1.19001 8.94009 0.710007 7.81008C0.240002 6.67007 0 5.58006 0 4.54005C0 3.86004 0.120001 3.21003 0.360004 2.61003C0.600006 2.00002 0.98001 1.44001 1.51002 0.940009C2.15002 0.310003 2.85003 0 3.59004 0C3.87004 0 4.15004 0.0600007 4.40004 0.180002C4.66005 0.300003 4.89005 0.480005 5.07005 0.740007L7.39007 4.01004C7.57008 4.26004 7.70008 4.49005 7.79008 4.71005C7.88008 4.92005 7.93008 5.13005 7.93008 5.32005C7.93008 5.56006 7.86008 5.80006 7.72008 6.03006C7.59008 6.26006 7.40007 6.50006 7.16007 6.74007L6.40006 7.53008C6.29006 7.64008 6.24006 7.77008 6.24006 7.93008C6.24006 8.01008 6.25006 8.08008 6.27006 8.16008C6.30006 8.24008 6.33006 8.30008 6.35006 8.36008C6.53007 8.69009 6.84007 9.12009 7.28007 9.6401C7.73008 10.1601 8.21008 10.6901 8.73009 11.2201C8.83009 11.3201 8.94009 11.4201 9.04009 11.5201C9.44009 11.9101 9.45009 12.5501 9.05009 12.9501Z" fill="white" />
                        <path d="M19.9698 16.3293C19.9698 16.6093 19.9198 16.8993 19.8198 17.1793C19.7898 17.2593 19.7598 17.3393 19.7198 17.4193C19.5498 17.7793 19.3298 18.1193 19.0398 18.4393C18.5498 18.9793 18.0098 19.3693 17.3998 19.6193C17.3898 19.6193 17.3798 19.6293 17.3698 19.6293C16.7798 19.8693 16.1398 19.9993 15.4498 19.9993C14.4297 19.9993 13.3397 19.7593 12.1897 19.2693C11.0397 18.7793 9.8897 18.1193 8.74969 17.2893C8.35968 16.9993 7.96969 16.7093 7.59969 16.3993L10.8697 13.1292C11.1497 13.3392 11.3997 13.4992 11.6097 13.6092C11.6597 13.6292 11.7197 13.6592 11.7897 13.6892C11.8697 13.7192 11.9497 13.7292 12.0397 13.7292C12.2097 13.7292 12.3397 13.6692 12.4497 13.5592L13.2097 12.8092C13.4597 12.5592 13.6997 12.3692 13.9297 12.2492C14.1597 12.1092 14.3897 12.0392 14.6397 12.0392C14.8297 12.0392 15.0297 12.0792 15.2498 12.1692C15.4698 12.2592 15.6998 12.3892 15.9498 12.5592L19.2598 14.9092C19.5198 15.0893 19.6998 15.2993 19.8098 15.5493C19.9098 15.7993 19.9698 16.0493 19.9698 16.3293Z" fill="white" />
                        </svg>
                    </a>
                    @endif
                    </div>


                    </div>
                    @endif

                <div class="flex items-center gap-[4px]">
                    <p class="text-[15px]">😡</p>
                    <a href="#modalSendReport" data-bs-toggle="modal" class="text-[#DC6F5A] text-[15px] underline underline-offset-2">{{ __('Report') }}</a>
                </div>
                </div>
            </div>

         </div>



    </div>
</div>

{{--
@include(Theme::getThemeNamespace('views.real-estate.single-layouts.partials.gallery-slider'), ['model' => $project])

<section class="flat-section pt-0 flat-property-detail">
    <div class="container">
        <div class="header-property-detail">
            <div class="content-top d-flex justify-content-between align-items-center">
                <div class="box-name">
                    {!! BaseHelper::clean($project->status_html) !!}
                    <h4 class="title link">
                        {!! BaseHelper::clean($project->name) !!}
                    </h4>
                </div>

                @if ($project->price_from || $project->price_to)
                    <div class="box-price d-flex align-items-center">
                        <h4>{{ $project->formatted_price }}</h4>
                    </div>
                @endif
            </div>
            @include(Theme::getThemeNamespace('views.real-estate.partials.meta'), ['model' => $project])
        </div>
        <div class="row">
            <div class="col-lg-8">
                {!! apply_filters('before_single_content_detail', null, $project) !!}

                @if ($project->content)
                    <div class="single-property-element single-property-desc">
                        <div class="h7 title fw-7">{{ __('Description') }}</div>
                        <div class="body-2 text-variant-1">
                            <div class="ck-content single-detail">
                                {!! BaseHelper::clean($project->content) !!}
                            </div>
                        </div>
                    </div>
                @endif
                @if ($videoUrl = $project->getMetaData('video_url', true))
                    <div class="single-property-element single-property-video">
                        <div class="h7 title fw-7">{{ __('Video') }}</div>
                        <div class="img-video">
                            <img src="{{ RvMedia::getImageUrl($project->getMetaData('video_thumbnail', true)) ?: \Xmetr\Theme\Supports\Youtube::getThumbnail($videoUrl) }}" alt="{{ $project->name }}">
                            <a href="{{ $videoUrl }}" @if(\Xmetr\Theme\Supports\Youtube::isYoutubeURL($videoUrl)) data-fancybox="gallery2" @endif class="btn-video">
                                <x-core::icon name="ti ti-player-play-filled" />
                            </a>
                        </div>
                    </div>
                @endif
                <div class="single-property-element single-property-overview">
                    <div class="h7 title fw-7">{{ __('Overview') }}</div>
                    <div class="row row-cols-2 row-cols-lg-3 g-3 g-lg-4 info-box">
                        <div class="col item">
                            <div class="box-icon w-52">
                                <x-core::icon name="ti ti-home" />
                            </div>
                            <div class="content">
                                <span class="label">{{ __('Project ID:') }}</span>
                                <span>{{ $project->unique_id ?: $project->getKey() }}</span>
                            </div>
                        </div>
                        @if($project->categories->isNotEmpty())
                            <div class="col item">
                                <div class="box-icon w-52">
                                    <x-core::icon name="ti ti-category" />
                                </div>
                                <div class="content">
                                    <span class="label">{{ __('Type:') }}</span>
                                    <span>
                                    {{ collect($project->categories)->pluck('name')->implode(', ') }}
                                </span>
                                </div>
                            </div>
                        @endif
                        @if ($project->investor->name)
                            <div class="col item">
                                <div class="box-icon w-52">
                                    <x-core::icon name="ti ti-category" />
                                </div>
                                <div class="content">
                                    <span class="label">{{ __('Investor:') }}</span>
                                    <span>{{ $project->investor->name }}</span>
                                </div>
                            </div>
                        @endif
                        @if ($project->number_block)
                            <div class="col item">
                                <div class="box-icon w-52">
                                    <x-core::icon name="ti ti-packages" />
                                </div>
                                <div class="content">
                                    <span class="label">{{ __('Blocks:') }}</span>
                                    <span>{{ number_format($project->number_block) }}</span>
                                </div>
                            </div>
                        @endif
                        @if ($project->number_floor)
                            <div class="col item">
                                <div class="box-icon w-52">
                                    <x-core::icon name="ti ti-stairs" />
                                </div>
                                <div class="content">
                                    <span class="label">{{ __('Floors:') }}</span>
                                    <span>{{ number_format($project->number_floor) }}</span>
                                </div>
                            </div>
                        @endif
                        @if ($project->number_flat)
                            <div class="col item">
                                <div class="box-icon w-52">
                                    <x-core::icon name="ti ti-building" />
                                </div>
                                <div class="content">
                                    <span class="label">{{ __('Flats:') }}</span>
                                    <span>{{ number_format($project->number_flat) }}</span>
                                </div>
                            </div>
                        @endif
                        @if ($project->square)
                            <div class="col item">
                                <div class="box-icon w-52">
                                    <x-core::icon name="ti ti-ruler-2" />
                                </div>
                                <div class="content">
                                    <span class="label">{{ __('Square:') }}</span>
                                    <span>{{ $project->square_text }}</span>
                                </div>
                            </div>
                        @endif
                        <div class="col item">
                            <div class="box-icon w-52">
                                <x-core::icon name="ti ti-coin" />
                            </div>
                            <div class="content">
                                <span class="label">{{ __('Price:') }}</span>
                                <span>
                                    @if ($project->price_from)
                                        {{ format_price($project->price_from, $project->currency) }}
                                    @endif
                                    @if ($project->price_to)
                                        - {{ format_price($project->price_to, $project->currency) }}
                                    @endif
                                </span>
                            </div>
                        </div>
                        @foreach ($project->customFields as $customField)
                            @continue(! $customField->value)
                            <div class="col item">
                                <div class="content">
                                    <span class="label">{!! BaseHelper::clean($customField->name) !!}:</span>
                                    <span>{!! BaseHelper::clean($customField->value) !!}</span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
                @if ($project->features->isNotEmpty())
                    <div class="single-property-element single-property-feature">
                        <div class="h7 title fw-7">{{ __('Amenities and features') }}</div>
                        <div class="box-feature">
                            <ul>
                                @foreach ($project->features as $feature)
                                    <li class="feature-item">
                                        @if($feature->icon)
                                            {!! BaseHelper::renderIcon($feature->icon) !!}
                                        @endif
                                        {{ $feature->name }}
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                @endif
                @if ($project->facilities->isNotEmpty())
                    <div class="single-property-element single-property-nearby">
                        <div class="h7 title fw-7">{{ __('What’s nearby?') }}</div>
                        <p class="body-2">{{ __("Explore nearby amenities to precisely locate your property and identify surrounding conveniences, providing a comprehensive overview of the living environment and the property's convenience.") }}</p>
                        <ul class="grid-3 box-nearby">
                            @foreach ($project->facilities as $facility)
                                <li class="item-nearby">
                                    <span class="label">
                                        @if($facility->icon)
                                            {!! BaseHelper::renderIcon($facility->icon) !!}
                                        @endif
                                        {{ $facility->name }}:
                                    </span>
                                    <span class="fw-7">{{ $facility->pivot->distance }}</span>
                                </li>
                            @endforeach
                        </ul>
                    </div>
                @endif
                <div class="single-property-element single-property-map">
                    <div class="h7 title fw-7">{{ __('Location') }}</div>
                    @if (theme_option('real_estate_show_map_on_single_detail_page', 'yes') === 'yes')
                        @if ($project->latitude && $project->longitude)
                            <div data-bb-toggle="detail-map" id="map" style="min-height: 400px;" data-tile-layer="{{ RealEstateHelper::getMapTileLayer() }}" data-center="{{ json_encode([$project->latitude, $project->longitude]) }}" data-map-icon="{{ $project->map_icon }}"></div>
                        @else
                            <iframe width="100%" style="min-height: 400px" src="https://maps.google.com/maps?q={{ urlencode($project->location) }}%20&t=&z=13&ie=UTF8&iwloc=&output=embed" frameborder="0" scrolling="no" marginheight="0" marginwidth="0"></iframe>
                        @endif
                    @endif
                    <ul class="info-map">
                        <li>
                            <div class="fw-7">{{ __('Address') }}</div>
                            <a class="mt-4 text-variant-1" href="https://www.google.com/maps/search/{{ urlencode($project->location) }}">
                                {{ $project->location ?: $project->short_address }}
                            </a>
                        </li>
                    </ul>

                    @include(Theme::getThemeNamespace('views.real-estate.partials.social-sharing'), ['model' => $project])
                </div>

                {!! apply_filters('after_single_content_detail', null, $project) !!}

                {!! apply_filters(BASE_FILTER_PUBLIC_COMMENT_AREA, null, $project) !!}

                @include(Theme::getThemeNamespace('views.real-estate.single-layouts.partials.reviews'), ['model' => $project])
            </div>
            <div class="col-lg-4">
                <div class="widget-sidebar fixed-sidebar wrapper-sidebar-right">
                        <div class="widget-box single-property-contact bg-surface">
                            <div class="h7 title fw-7">{{ __('Contact Agency') }}</div>

                            @if (! RealEstateHelper::hideAgentInfoInPropertyDetailPage() && ($account = $project->author))
                                <div class="box-avatar">
                                    <div class="avatar avt-100 round">
                                        <a href="{{ $account->url }}" class="d-block">
                                            {{ RvMedia::image($account->avatar->url ?: $account->avatar_url, $account->name) }}
                                        </a>
                                    </div>
                                    <div class="info line-clamp-1">
                                        <div class="text-1 name">
                                            <a href="{{ $account->url }}">{{ $account->name }}</a>
                                        </div>
                                        @if ($account->phone && ! setting('real_estate_hide_agency_phone', false))
                                            <a href="tel:{{ $account->phone }}" class="info-item">{{ $account->phone }}</a>
                                        @elseif($hotline = theme_option('hotline'))
                                            <a href="tel:{{ $hotline }}" class="info-item">{{ $hotline }}</a>
                                        @endif
                                        @if ($account->email && ! setting('real_estate_hide_agency_email', false))
                                            <a href="mailto:{{ $account->email }}" class="info-item">{{ $account->email }}</a>
                                        @endif
                                    </div>
                                </div>
                            @endif

                            {!! apply_filters('project_right_details_info', null, $project) !!}

                            {!! apply_filters('before_consult_form', null, $project) !!}

                            {!! \Xmetr\RealEstate\Forms\Fronts\ConsultForm::create()
                                ->formClass('contact-form')
                                ->setFormInputWrapperClass('ip-group')
                                ->modify('content', 'textarea', ['attr' => ['class' => '']])
                                ->modify('submit', 'submit', ['attr' => ['class' => 'tf-btn primary w-100']])
                                ->add('type', 'hidden', ['attr' => ['value' => 'project']])
                                ->add('data_id', 'hidden', ['attr' => ['value' => $project->getKey()]])
                                ->addBefore('content', 'data_name', 'text', ['label' => false, 'attr' => ['value' => $project->name, 'disabled' => true]])
                                ->renderForm()
                            !!}

                            {!! apply_filters('after_consult_form', null, $project) !!}
                        </div>
                </div>
            </div>
        </div>
    </div>
</section>
 --}}

@php
    $relatedProperties = app(\Xmetr\RealEstate\Repositories\Interfaces\PropertyInterface::class)
    ->getPropertiesByConditions(
        [
            're_properties.project_id' => $project->getKey(),
        ],
        8,
        \Xmetr\RealEstate\Facades\RealEstateHelper::getPropertyRelationsQuery(),
    );
@endphp

@if ($relatedProperties->isNotEmpty())
    <section class="flat-section pt-0 flat-latest-property">
        <div class="container">
            <div class="box-title text-center">
                {{-- <div class="text-subtitle text-primary">{{ __('Latest Properties') }}</div> --}}
                <h2 class="section-title mt-4">{{ __('Properties in project ":name"', ['name' => $project->name]) }}</h2>
            </div>
            <div class="swiper tf-latest-property" data-preview-lg="3" data-preview-md="2" data-preview-sm="2" data-space="30" data-loop="true">
                <div class="swiper-wrapper">
                    @foreach($relatedProperties as $property)
                        <div class="swiper-slide">
                            @include(Theme::getThemeNamespace('views.real-estate.properties.item-grid'), ['property' => $property, 'class' => 'style-2'])
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </section>
@endif


<template id="map-popup-content">
    <div class="map-popup-content">
        <a class="map-popup-content-thumb" href="{{ $project->url }}">
            {{ RvMedia::image($project->image_thumb, $project->name) }}
            {!! BaseHelper::clean($project->status_html) !!}
        </a>
        <div class="map-popup-content__details">
            <h5 class="map-popup-content__title">
                <a href="{{ $project->url }}" target="_blank" class="map-popup-content__link">{{ $project->name }}</a>
            </h5>
            <div class="map-popup-content__price">{{ $project->category_name }}</div>
            <div class="map-popup-content__city">
                <x-core::icon name="ti ti-map-pin" />
                {{ $project->short_address }}
            </div>
        </div>
    </div>
</template>
