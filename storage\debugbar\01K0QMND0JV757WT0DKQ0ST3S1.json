{"__meta": {"id": "01K0QMND0JV757WT0DKQ0ST3S1", "datetime": "2025-07-21 23:21:21", "utime": **********.683158, "method": "GET", "uri": "/admin/real-estate/projects/edit/25", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753140067.995939, "end": **********.683181, "duration": 13.687242031097412, "duration_str": "13.69s", "measures": [{"label": "Booting", "start": 1753140067.995939, "relative_start": 0, "end": **********.781665, "relative_end": **********.781665, "duration": 0.****************, "duration_str": "786ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.781676, "relative_start": 0.****************, "end": **********.683184, "relative_end": 2.86102294921875e-06, "duration": 12.**************, "duration_str": "12.9s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.798352, "relative_start": 0.****************, "end": **********.814923, "relative_end": **********.814923, "duration": 0.***************, "duration_str": "16.57ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: plugins/real-estate::partials.form-features", "start": **********.937544, "relative_start": 0.****************, "end": **********.937544, "relative_end": **********.937544, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.082773, "relative_start": 1.****************, "end": **********.082773, "relative_end": **********.082773, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.087326, "relative_start": 1.0913870334625244, "end": **********.087326, "relative_end": **********.087326, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.091434, "relative_start": 1.0954949855804443, "end": **********.091434, "relative_end": **********.091434, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.094067, "relative_start": 1.098128080368042, "end": **********.094067, "relative_end": **********.094067, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.096101, "relative_start": 1.1001620292663574, "end": **********.096101, "relative_end": **********.096101, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.098945, "relative_start": 1.1030058860778809, "end": **********.098945, "relative_end": **********.098945, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.103434, "relative_start": 1.1074950695037842, "end": **********.103434, "relative_end": **********.103434, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.106252, "relative_start": 1.1103129386901855, "end": **********.106252, "relative_end": **********.106252, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.108979, "relative_start": 1.1130399703979492, "end": **********.108979, "relative_end": **********.108979, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.112706, "relative_start": 1.1167669296264648, "end": **********.112706, "relative_end": **********.112706, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.115963, "relative_start": 1.1200239658355713, "end": **********.115963, "relative_end": **********.115963, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.119024, "relative_start": 1.1230850219726562, "end": **********.119024, "relative_end": **********.119024, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.122001, "relative_start": 1.1260619163513184, "end": **********.122001, "relative_end": **********.122001, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.125274, "relative_start": 1.1293349266052246, "end": **********.125274, "relative_end": **********.125274, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.127804, "relative_start": 1.1318650245666504, "end": **********.127804, "relative_end": **********.127804, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.130177, "relative_start": 1.1342380046844482, "end": **********.130177, "relative_end": **********.130177, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.1322, "relative_start": 1.136260986328125, "end": **********.1322, "relative_end": **********.1322, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.134218, "relative_start": 1.1382789611816406, "end": **********.134218, "relative_end": **********.134218, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.136132, "relative_start": 1.140192985534668, "end": **********.136132, "relative_end": **********.136132, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.138042, "relative_start": 1.1421029567718506, "end": **********.138042, "relative_end": **********.138042, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.142141, "relative_start": 1.1462020874023438, "end": **********.142141, "relative_end": **********.142141, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.form", "start": **********.187343, "relative_start": 1.191403865814209, "end": **********.187343, "relative_end": **********.187343, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language::partials.notification", "start": **********.914162, "relative_start": 1.9182229042053223, "end": **********.914162, "relative_end": **********.914162, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::alert", "start": **********.054801, "relative_start": 2.0588619709014893, "end": **********.054801, "relative_end": **********.054801, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::920a4a8abfd99078c24a8f8db36a19f4", "start": **********.250131, "relative_start": 2.2541918754577637, "end": **********.250131, "relative_end": **********.250131, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": **********.268821, "relative_start": 2.2728819847106934, "end": **********.268821, "relative_end": **********.268821, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.410083, "relative_start": 2.4141440391540527, "end": **********.410083, "relative_end": **********.410083, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.410829, "relative_start": 2.4148900508880615, "end": **********.410829, "relative_end": **********.410829, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.413279, "relative_start": 2.417340040206909, "end": **********.413279, "relative_end": **********.413279, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.472908, "relative_start": 2.476969003677368, "end": **********.472908, "relative_end": **********.472908, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.52438, "relative_start": 2.5284409523010254, "end": **********.52438, "relative_end": **********.52438, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/slug::forms.fields.permalink", "start": **********.527548, "relative_start": 2.531609058380127, "end": **********.527548, "relative_end": **********.527548, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/slug::permalink", "start": **********.601295, "relative_start": 2.605355978012085, "end": **********.601295, "relative_end": **********.601295, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0dc66e2ea833d19323a8163033ac48c1", "start": 1753140071.119254, "relative_start": 3.1233150959014893, "end": 1753140071.119254, "relative_end": 1753140071.119254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.text-input", "start": 1753140071.137615, "relative_start": 3.1416759490966797, "end": 1753140071.137615, "relative_end": 1753140071.137615, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1753140071.138756, "relative_start": 3.1428170204162598, "end": 1753140071.138756, "relative_end": 1753140071.138756, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.error", "start": 1753140071.139245, "relative_start": 3.143306016921997, "end": 1753140071.139245, "relative_end": 1753140071.139245, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": 1753140071.139611, "relative_start": 3.143671989440918, "end": 1753140071.139611, "relative_end": 1753140071.139611, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.helper-text", "start": 1753140071.140143, "relative_start": 3.1442039012908936, "end": 1753140071.140143, "relative_end": 1753140071.140143, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.error", "start": 1753140071.170447, "relative_start": 3.1745080947875977, "end": 1753140071.170447, "relative_end": 1753140071.170447, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1753140071.194726, "relative_start": 3.198786973953247, "end": 1753140071.194726, "relative_end": 1753140071.194726, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.textarea", "start": 1753140071.195325, "relative_start": 3.19938588142395, "end": 1753140071.195325, "relative_end": 1753140071.195325, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1753140071.354329, "relative_start": 3.3583900928497314, "end": 1753140071.354329, "relative_end": 1753140071.354329, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1753140071.354667, "relative_start": 3.3587279319763184, "end": 1753140071.354667, "relative_end": 1753140071.354667, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1753140071.355173, "relative_start": 3.359234094619751, "end": 1753140071.355173, "relative_end": 1753140071.355173, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1753140071.355405, "relative_start": 3.359466075897217, "end": 1753140071.355405, "relative_end": 1753140071.355405, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1753140071.355674, "relative_start": 3.3597350120544434, "end": 1753140071.355674, "relative_end": 1753140071.355674, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.on-off", "start": 1753140071.35645, "relative_start": 3.360511064529419, "end": 1753140071.35645, "relative_end": 1753140071.35645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.on-off", "start": 1753140071.527008, "relative_start": 3.531069040298462, "end": 1753140071.527008, "relative_end": 1753140071.527008, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.toggle", "start": 1753140071.691341, "relative_start": 3.695401906967163, "end": 1753140071.691341, "relative_end": 1753140071.691341, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1753140071.69205, "relative_start": 3.696110963821411, "end": 1753140071.69205, "relative_end": 1753140071.69205, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1753140071.692648, "relative_start": 3.696708917617798, "end": 1753140071.692648, "relative_end": 1753140071.692648, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1753140071.692952, "relative_start": 3.6970129013061523, "end": 1753140071.692952, "relative_end": 1753140071.692952, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1753140071.693273, "relative_start": 3.697334051132202, "end": 1753140071.693273, "relative_end": 1753140071.693273, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.editor", "start": 1753140071.694279, "relative_start": 3.6983399391174316, "end": 1753140071.694279, "relative_end": 1753140071.694279, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1753140071.892858, "relative_start": 3.896919012069702, "end": 1753140071.892858, "relative_end": 1753140071.892858, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.editor", "start": 1753140071.893501, "relative_start": 3.897562026977539, "end": 1753140071.893501, "relative_end": 1753140071.893501, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140072.233638, "relative_start": 4.237699031829834, "end": 1753140072.233638, "relative_end": 1753140072.233638, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140072.235943, "relative_start": 4.240004062652588, "end": 1753140072.235943, "relative_end": 1753140072.235943, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7b4681f3a79c415079e436b7e5e9f3ac", "start": 1753140072.245891, "relative_start": 4.249952077865601, "end": 1753140072.245891, "relative_end": 1753140072.245891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/shortcode::partials.shortcode-button", "start": 1753140072.26861, "relative_start": 4.2726709842681885, "end": 1753140072.26861, "relative_end": 1753140072.26861, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5d3f310865558ef9371c1fb4b468d742", "start": 1753140072.49062, "relative_start": 4.494680881500244, "end": 1753140072.49062, "relative_end": 1753140072.49062, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140072.506938, "relative_start": 4.510998964309692, "end": 1753140072.506938, "relative_end": 1753140072.506938, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/shortcode::partials.shortcode-modal", "start": 1753140072.508497, "relative_start": 4.5125579833984375, "end": 1753140072.508497, "relative_end": 1753140072.508497, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3cec1c87224222bda738c53f782c5bc1", "start": 1753140074.177965, "relative_start": 6.182025909423828, "end": 1753140074.177965, "relative_end": 1753140074.177965, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::123a2f76ca745c01f392c79275e8e77b", "start": 1753140074.178729, "relative_start": 6.182790040969849, "end": 1753140074.178729, "relative_end": 1753140074.178729, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.text-input", "start": 1753140074.17896, "relative_start": 6.183021068572998, "end": 1753140074.17896, "relative_end": 1753140074.17896, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.error", "start": 1753140074.18074, "relative_start": 6.18480110168457, "end": 1753140074.18074, "relative_end": 1753140074.18074, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": 1753140074.181341, "relative_start": 6.185401916503906, "end": 1753140074.181341, "relative_end": 1753140074.181341, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": 1753140074.182378, "relative_start": 6.186439037322998, "end": 1753140074.182378, "relative_end": 1753140074.182378, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.subtitle", "start": 1753140074.184559, "relative_start": 6.188620090484619, "end": 1753140074.184559, "relative_end": 1753140074.184559, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140074.213324, "relative_start": 6.2173850536346436, "end": 1753140074.213324, "relative_end": 1753140074.213324, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": 1753140074.21459, "relative_start": 6.218651056289673, "end": 1753140074.21459, "relative_end": 1753140074.21459, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": 1753140074.215519, "relative_start": 6.2195799350738525, "end": 1753140074.215519, "relative_end": 1753140074.215519, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": 1753140074.216126, "relative_start": 6.220186948776245, "end": 1753140074.216126, "relative_end": 1753140074.216126, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.subtitle", "start": 1753140074.216587, "relative_start": 6.2206480503082275, "end": 1753140074.216587, "relative_end": 1753140074.216587, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140074.216896, "relative_start": 6.220957040786743, "end": 1753140074.216896, "relative_end": 1753140074.216896, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": 1753140074.217657, "relative_start": 6.221718072891235, "end": 1753140074.217657, "relative_end": 1753140074.217657, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": 1753140074.21793, "relative_start": 6.221991062164307, "end": 1753140074.21793, "relative_end": 1753140074.21793, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": 1753140074.218401, "relative_start": 6.222461938858032, "end": 1753140074.218401, "relative_end": 1753140074.218401, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.subtitle", "start": 1753140074.218747, "relative_start": 6.222807884216309, "end": 1753140074.218747, "relative_end": 1753140074.218747, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140074.219019, "relative_start": 6.2230799198150635, "end": 1753140074.219019, "relative_end": 1753140074.219019, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": 1753140074.219665, "relative_start": 6.223726034164429, "end": 1753140074.219665, "relative_end": 1753140074.219665, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": 1753140074.219902, "relative_start": 6.223963022232056, "end": 1753140074.219902, "relative_end": 1753140074.219902, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": 1753140074.220338, "relative_start": 6.224399089813232, "end": 1753140074.220338, "relative_end": 1753140074.220338, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.subtitle", "start": 1753140074.220659, "relative_start": 6.224720001220703, "end": 1753140074.220659, "relative_end": 1753140074.220659, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140074.221047, "relative_start": 6.225107908248901, "end": 1753140074.221047, "relative_end": 1753140074.221047, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": 1753140074.221657, "relative_start": 6.225718021392822, "end": 1753140074.221657, "relative_end": 1753140074.221657, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": 1753140074.221885, "relative_start": 6.225945949554443, "end": 1753140074.221885, "relative_end": 1753140074.221885, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": 1753140074.222278, "relative_start": 6.226339101791382, "end": 1753140074.222278, "relative_end": 1753140074.222278, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.subtitle", "start": 1753140074.222611, "relative_start": 6.226671934127808, "end": 1753140074.222611, "relative_end": 1753140074.222611, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140074.222863, "relative_start": 6.226923942565918, "end": 1753140074.222863, "relative_end": 1753140074.222863, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": 1753140074.223723, "relative_start": 6.227783918380737, "end": 1753140074.223723, "relative_end": 1753140074.223723, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": 1753140074.224002, "relative_start": 6.228062868118286, "end": 1753140074.224002, "relative_end": 1753140074.224002, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": 1753140074.224491, "relative_start": 6.228551864624023, "end": 1753140074.224491, "relative_end": 1753140074.224491, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.subtitle", "start": 1753140074.224857, "relative_start": 6.228918075561523, "end": 1753140074.224857, "relative_end": 1753140074.224857, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140074.225153, "relative_start": 6.2292139530181885, "end": 1753140074.225153, "relative_end": 1753140074.225153, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": 1753140074.225784, "relative_start": 6.22984504699707, "end": 1753140074.225784, "relative_end": 1753140074.225784, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": 1753140074.226047, "relative_start": 6.230108022689819, "end": 1753140074.226047, "relative_end": 1753140074.226047, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": 1753140074.226539, "relative_start": 6.230599880218506, "end": 1753140074.226539, "relative_end": 1753140074.226539, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.subtitle", "start": 1753140074.226874, "relative_start": 6.230935096740723, "end": 1753140074.226874, "relative_end": 1753140074.226874, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140074.227175, "relative_start": 6.231235980987549, "end": 1753140074.227175, "relative_end": 1753140074.227175, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": 1753140074.22791, "relative_start": 6.231971025466919, "end": 1753140074.22791, "relative_end": 1753140074.22791, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": 1753140074.228162, "relative_start": 6.232223033905029, "end": 1753140074.228162, "relative_end": 1753140074.228162, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": 1753140074.228657, "relative_start": 6.232717990875244, "end": 1753140074.228657, "relative_end": 1753140074.228657, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.subtitle", "start": 1753140074.22899, "relative_start": 6.233051061630249, "end": 1753140074.22899, "relative_end": 1753140074.22899, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140074.229302, "relative_start": 6.233362913131714, "end": 1753140074.229302, "relative_end": 1753140074.229302, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": 1753140074.230471, "relative_start": 6.234531879425049, "end": 1753140074.230471, "relative_end": 1753140074.230471, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": 1753140074.230895, "relative_start": 6.2349560260772705, "end": 1753140074.230895, "relative_end": 1753140074.230895, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": 1753140074.231451, "relative_start": 6.235512018203735, "end": 1753140074.231451, "relative_end": 1753140074.231451, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.subtitle", "start": 1753140074.231836, "relative_start": 6.235897064208984, "end": 1753140074.231836, "relative_end": 1753140074.231836, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140074.232123, "relative_start": 6.2361838817596436, "end": 1753140074.232123, "relative_end": 1753140074.232123, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": 1753140074.232926, "relative_start": 6.236986875534058, "end": 1753140074.232926, "relative_end": 1753140074.232926, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": 1753140074.233169, "relative_start": 6.237230062484741, "end": 1753140074.233169, "relative_end": 1753140074.233169, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": 1753140074.23361, "relative_start": 6.2376708984375, "end": 1753140074.23361, "relative_end": 1753140074.23361, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.subtitle", "start": 1753140074.234036, "relative_start": 6.2380969524383545, "end": 1753140074.234036, "relative_end": 1753140074.234036, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140074.234332, "relative_start": 6.238393068313599, "end": 1753140074.234332, "relative_end": 1753140074.234332, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": 1753140074.234965, "relative_start": 6.239026069641113, "end": 1753140074.234965, "relative_end": 1753140074.234965, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": 1753140074.235222, "relative_start": 6.239283084869385, "end": 1753140074.235222, "relative_end": 1753140074.235222, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": 1753140074.23564, "relative_start": 6.23970103263855, "end": 1753140074.23564, "relative_end": 1753140074.23564, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.subtitle", "start": 1753140074.235966, "relative_start": 6.240026950836182, "end": 1753140074.235966, "relative_end": 1753140074.235966, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140074.236229, "relative_start": 6.240289926528931, "end": 1753140074.236229, "relative_end": 1753140074.236229, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": 1753140074.236838, "relative_start": 6.240899085998535, "end": 1753140074.236838, "relative_end": 1753140074.236838, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": 1753140074.237066, "relative_start": 6.241127014160156, "end": 1753140074.237066, "relative_end": 1753140074.237066, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": 1753140074.237627, "relative_start": 6.241688013076782, "end": 1753140074.237627, "relative_end": 1753140074.237627, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.subtitle", "start": 1753140074.237952, "relative_start": 6.242012977600098, "end": 1753140074.237952, "relative_end": 1753140074.237952, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140074.238372, "relative_start": 6.242433071136475, "end": 1753140074.238372, "relative_end": 1753140074.238372, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": 1753140074.239485, "relative_start": 6.243546009063721, "end": 1753140074.239485, "relative_end": 1753140074.239485, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": 1753140074.239871, "relative_start": 6.243932008743286, "end": 1753140074.239871, "relative_end": 1753140074.239871, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": 1753140074.240376, "relative_start": 6.244436979293823, "end": 1753140074.240376, "relative_end": 1753140074.240376, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.subtitle", "start": 1753140074.240881, "relative_start": 6.24494194984436, "end": 1753140074.240881, "relative_end": 1753140074.240881, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140074.241184, "relative_start": 6.245244979858398, "end": 1753140074.241184, "relative_end": 1753140074.241184, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": 1753140074.241843, "relative_start": 6.245903968811035, "end": 1753140074.241843, "relative_end": 1753140074.241843, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": 1753140074.242097, "relative_start": 6.246157884597778, "end": 1753140074.242097, "relative_end": 1753140074.242097, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": 1753140074.242523, "relative_start": 6.246583938598633, "end": 1753140074.242523, "relative_end": 1753140074.242523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.subtitle", "start": 1753140074.242857, "relative_start": 6.246917963027954, "end": 1753140074.242857, "relative_end": 1753140074.242857, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140074.243125, "relative_start": 6.247185945510864, "end": 1753140074.243125, "relative_end": 1753140074.243125, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": 1753140074.243742, "relative_start": 6.247802972793579, "end": 1753140074.243742, "relative_end": 1753140074.243742, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": 1753140074.243985, "relative_start": 6.248045921325684, "end": 1753140074.243985, "relative_end": 1753140074.243985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": 1753140074.244424, "relative_start": 6.248485088348389, "end": 1753140074.244424, "relative_end": 1753140074.244424, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.subtitle", "start": 1753140074.244752, "relative_start": 6.248812913894653, "end": 1753140074.244752, "relative_end": 1753140074.244752, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140074.245014, "relative_start": 6.249074935913086, "end": 1753140074.245014, "relative_end": 1753140074.245014, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": 1753140074.245651, "relative_start": 6.249711990356445, "end": 1753140074.245651, "relative_end": 1753140074.245651, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": 1753140074.245901, "relative_start": 6.249962091445923, "end": 1753140074.245901, "relative_end": 1753140074.245901, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": 1753140074.246494, "relative_start": 6.250555038452148, "end": 1753140074.246494, "relative_end": 1753140074.246494, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.subtitle", "start": 1753140074.246954, "relative_start": 6.251014947891235, "end": 1753140074.246954, "relative_end": 1753140074.246954, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140074.247294, "relative_start": 6.251354932785034, "end": 1753140074.247294, "relative_end": 1753140074.247294, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": 1753140074.248273, "relative_start": 6.252333879470825, "end": 1753140074.248273, "relative_end": 1753140074.248273, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": 1753140074.248585, "relative_start": 6.252645969390869, "end": 1753140074.248585, "relative_end": 1753140074.248585, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": 1753140074.249171, "relative_start": 6.253232002258301, "end": 1753140074.249171, "relative_end": 1753140074.249171, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.subtitle", "start": 1753140074.249637, "relative_start": 6.253697872161865, "end": 1753140074.249637, "relative_end": 1753140074.249637, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140074.249891, "relative_start": 6.2539520263671875, "end": 1753140074.249891, "relative_end": 1753140074.249891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": 1753140074.250524, "relative_start": 6.254585027694702, "end": 1753140074.250524, "relative_end": 1753140074.250524, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": 1753140074.25077, "relative_start": 6.254831075668335, "end": 1753140074.25077, "relative_end": 1753140074.25077, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": 1753140074.251185, "relative_start": 6.255245923995972, "end": 1753140074.251185, "relative_end": 1753140074.251185, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.subtitle", "start": 1753140074.25157, "relative_start": 6.255630970001221, "end": 1753140074.25157, "relative_end": 1753140074.25157, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140074.251844, "relative_start": 6.255904912948608, "end": 1753140074.251844, "relative_end": 1753140074.251844, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": 1753140074.25258, "relative_start": 6.256640911102295, "end": 1753140074.25258, "relative_end": 1753140074.25258, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": 1753140074.252832, "relative_start": 6.256892919540405, "end": 1753140074.252832, "relative_end": 1753140074.252832, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": 1753140074.253408, "relative_start": 6.257468938827515, "end": 1753140074.253408, "relative_end": 1753140074.253408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.subtitle", "start": 1753140074.253786, "relative_start": 6.25784707069397, "end": 1753140074.253786, "relative_end": 1753140074.253786, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140074.254061, "relative_start": 6.258121967315674, "end": 1753140074.254061, "relative_end": 1753140074.254061, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": 1753140074.254732, "relative_start": 6.258792877197266, "end": 1753140074.254732, "relative_end": 1753140074.254732, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": 1753140074.255021, "relative_start": 6.259082078933716, "end": 1753140074.255021, "relative_end": 1753140074.255021, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": 1753140074.255463, "relative_start": 6.259523868560791, "end": 1753140074.255463, "relative_end": 1753140074.255463, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.subtitle", "start": 1753140074.255823, "relative_start": 6.259883880615234, "end": 1753140074.255823, "relative_end": 1753140074.255823, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140074.256088, "relative_start": 6.260149002075195, "end": 1753140074.256088, "relative_end": 1753140074.256088, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": 1753140074.256744, "relative_start": 6.260804891586304, "end": 1753140074.256744, "relative_end": 1753140074.256744, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": 1753140074.256984, "relative_start": 6.261044979095459, "end": 1753140074.256984, "relative_end": 1753140074.256984, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": 1753140074.257428, "relative_start": 6.261488914489746, "end": 1753140074.257428, "relative_end": 1753140074.257428, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.subtitle", "start": 1753140074.257764, "relative_start": 6.261825084686279, "end": 1753140074.257764, "relative_end": 1753140074.257764, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140074.258158, "relative_start": 6.262218952178955, "end": 1753140074.258158, "relative_end": 1753140074.258158, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": 1753140074.258863, "relative_start": 6.262923955917358, "end": 1753140074.258863, "relative_end": 1753140074.258863, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": 1753140074.259102, "relative_start": 6.263163089752197, "end": 1753140074.259102, "relative_end": 1753140074.259102, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": 1753140074.259517, "relative_start": 6.263577938079834, "end": 1753140074.259517, "relative_end": 1753140074.259517, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.subtitle", "start": 1753140074.259841, "relative_start": 6.263901948928833, "end": 1753140074.259841, "relative_end": 1753140074.259841, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140074.260098, "relative_start": 6.2641589641571045, "end": 1753140074.260098, "relative_end": 1753140074.260098, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": 1753140074.260695, "relative_start": 6.264755964279175, "end": 1753140074.260695, "relative_end": 1753140074.260695, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": 1753140074.260926, "relative_start": 6.264986991882324, "end": 1753140074.260926, "relative_end": 1753140074.260926, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": 1753140074.261314, "relative_start": 6.2653748989105225, "end": 1753140074.261314, "relative_end": 1753140074.261314, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.subtitle", "start": 1753140074.26163, "relative_start": 6.265691041946411, "end": 1753140074.26163, "relative_end": 1753140074.26163, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140074.261885, "relative_start": 6.265945911407471, "end": 1753140074.261885, "relative_end": 1753140074.261885, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": 1753140074.262489, "relative_start": 6.266550064086914, "end": 1753140074.262489, "relative_end": 1753140074.262489, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": 1753140074.262828, "relative_start": 6.2668890953063965, "end": 1753140074.262828, "relative_end": 1753140074.262828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": 1753140074.263384, "relative_start": 6.267445087432861, "end": 1753140074.263384, "relative_end": 1753140074.263384, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.subtitle", "start": 1753140074.263787, "relative_start": 6.267848014831543, "end": 1753140074.263787, "relative_end": 1753140074.263787, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140074.26408, "relative_start": 6.268141031265259, "end": 1753140074.26408, "relative_end": 1753140074.26408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": 1753140074.264789, "relative_start": 6.268850088119507, "end": 1753140074.264789, "relative_end": 1753140074.264789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": 1753140074.26505, "relative_start": 6.269110918045044, "end": 1753140074.26505, "relative_end": 1753140074.26505, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": 1753140074.265754, "relative_start": 6.269814968109131, "end": 1753140074.265754, "relative_end": 1753140074.265754, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.subtitle", "start": 1753140074.266127, "relative_start": 6.270188093185425, "end": 1753140074.266127, "relative_end": 1753140074.266127, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140074.266388, "relative_start": 6.270448923110962, "end": 1753140074.266388, "relative_end": 1753140074.266388, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": 1753140074.267035, "relative_start": 6.2710959911346436, "end": 1753140074.267035, "relative_end": 1753140074.267035, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": 1753140074.26731, "relative_start": 6.271370887756348, "end": 1753140074.26731, "relative_end": 1753140074.26731, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": 1753140074.26777, "relative_start": 6.271831035614014, "end": 1753140074.26777, "relative_end": 1753140074.26777, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.subtitle", "start": 1753140074.268099, "relative_start": 6.272160053253174, "end": 1753140074.268099, "relative_end": 1753140074.268099, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140074.268526, "relative_start": 6.272587060928345, "end": 1753140074.268526, "relative_end": 1753140074.268526, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": 1753140074.2695, "relative_start": 6.273561000823975, "end": 1753140074.2695, "relative_end": 1753140074.2695, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": 1753140074.26983, "relative_start": 6.273890972137451, "end": 1753140074.26983, "relative_end": 1753140074.26983, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": 1753140074.270448, "relative_start": 6.274508953094482, "end": 1753140074.270448, "relative_end": 1753140074.270448, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.subtitle", "start": 1753140074.27091, "relative_start": 6.274971008300781, "end": 1753140074.27091, "relative_end": 1753140074.27091, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140074.271288, "relative_start": 6.275348901748657, "end": 1753140074.271288, "relative_end": 1753140074.271288, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": 1753140074.27219, "relative_start": 6.2762510776519775, "end": 1753140074.27219, "relative_end": 1753140074.27219, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": 1753140074.272477, "relative_start": 6.276537895202637, "end": 1753140074.272477, "relative_end": 1753140074.272477, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::empty-state", "start": 1753140074.272973, "relative_start": 6.277034044265747, "end": 1753140074.272973, "relative_end": 1753140074.272973, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::63cd1411f8b671aa6ea66cfd91c420b5", "start": 1753140074.479011, "relative_start": 6.**************, "end": 1753140074.479011, "relative_end": 1753140074.479011, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140074.502515, "relative_start": 6.506576061248779, "end": 1753140074.502515, "relative_end": 1753140074.502515, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140074.503307, "relative_start": 6.507368087768555, "end": 1753140074.503307, "relative_end": 1753140074.503307, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal", "start": 1753140074.503973, "relative_start": 6.508033990859985, "end": 1753140074.503973, "relative_end": 1753140074.503973, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close-button", "start": 1753140074.505252, "relative_start": 6.509312868118286, "end": 1753140074.505252, "relative_end": 1753140074.505252, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140074.506603, "relative_start": 6.510663986206055, "end": 1753140074.506603, "relative_end": 1753140074.506603, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140074.507768, "relative_start": 6.511828899383545, "end": 1753140074.507768, "relative_end": 1753140074.507768, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal", "start": 1753140074.508576, "relative_start": 6.51263689994812, "end": 1753140074.508576, "relative_end": 1753140074.508576, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close-button", "start": 1753140074.509431, "relative_start": 6.513491868972778, "end": 1753140074.509431, "relative_end": 1753140074.509431, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.ckeditor", "start": 1753140074.510227, "relative_start": 6.514287948608398, "end": 1753140074.510227, "relative_end": 1753140074.510227, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1753140074.551666, "relative_start": 6.555727005004883, "end": 1753140074.551666, "relative_end": 1753140074.551666, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1753140074.552369, "relative_start": 6.556430101394653, "end": 1753140074.552369, "relative_end": 1753140074.552369, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1753140074.552791, "relative_start": 6.556852102279663, "end": 1753140074.552791, "relative_end": 1753140074.552791, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1753140074.553229, "relative_start": 6.557290077209473, "end": 1753140074.553229, "relative_end": 1753140074.553229, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.media-images", "start": 1753140074.553951, "relative_start": 6.558012008666992, "end": 1753140074.553951, "relative_end": 1753140074.553951, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1753140074.694965, "relative_start": 6.699025869369507, "end": 1753140074.694965, "relative_end": 1753140074.694965, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.images", "start": 1753140074.695457, "relative_start": 6.6995179653167725, "end": 1753140074.695457, "relative_end": 1753140074.695457, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.images", "start": 1753140074.818428, "relative_start": 6.822489023208618, "end": 1753140074.818428, "relative_end": 1753140074.818428, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6e68593c2024c25e21ccc0ea08c838db", "start": 1753140075.279498, "relative_start": 7.283559083938599, "end": 1753140075.279498, "relative_end": 1753140075.279498, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::image", "start": 1753140075.419866, "relative_start": 7.423927068710327, "end": 1753140075.419866, "relative_end": 1753140075.419866, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140075.477112, "relative_start": 7.481173038482666, "end": 1753140075.477112, "relative_end": 1753140075.477112, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1c3e3c2b5f3306f2c083e21b9339fe63", "start": 1753140075.478671, "relative_start": 7.482732057571411, "end": 1753140075.478671, "relative_end": 1753140075.478671, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::image", "start": 1753140075.479509, "relative_start": 7.483570098876953, "end": 1753140075.479509, "relative_end": 1753140075.479509, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140075.479995, "relative_start": 7.484055995941162, "end": 1753140075.479995, "relative_end": 1753140075.479995, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1c3e3c2b5f3306f2c083e21b9339fe63", "start": 1753140075.481059, "relative_start": 7.485120058059692, "end": 1753140075.481059, "relative_end": 1753140075.481059, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::image", "start": 1753140075.482057, "relative_start": 7.4861180782318115, "end": 1753140075.482057, "relative_end": 1753140075.482057, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140075.482769, "relative_start": 7.486829996109009, "end": 1753140075.482769, "relative_end": 1753140075.482769, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1c3e3c2b5f3306f2c083e21b9339fe63", "start": 1753140075.483944, "relative_start": 7.488004922866821, "end": 1753140075.483944, "relative_end": 1753140075.483944, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::image", "start": 1753140075.4848, "relative_start": 7.488861083984375, "end": 1753140075.4848, "relative_end": 1753140075.4848, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140075.485287, "relative_start": 7.4893479347229, "end": 1753140075.485287, "relative_end": 1753140075.485287, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1c3e3c2b5f3306f2c083e21b9339fe63", "start": 1753140075.485971, "relative_start": 7.490031957626343, "end": 1753140075.485971, "relative_end": 1753140075.485971, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::image", "start": 1753140075.486516, "relative_start": 7.490576982498169, "end": 1753140075.486516, "relative_end": 1753140075.486516, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140075.486902, "relative_start": 7.490962982177734, "end": 1753140075.486902, "relative_end": 1753140075.486902, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1c3e3c2b5f3306f2c083e21b9339fe63", "start": 1753140075.487532, "relative_start": 7.491592884063721, "end": 1753140075.487532, "relative_end": 1753140075.487532, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::image", "start": 1753140075.488096, "relative_start": 7.492156982421875, "end": 1753140075.488096, "relative_end": 1753140075.488096, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140075.488482, "relative_start": 7.49254298210144, "end": 1753140075.488482, "relative_end": 1753140075.488482, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1c3e3c2b5f3306f2c083e21b9339fe63", "start": 1753140075.48914, "relative_start": 7.493201017379761, "end": 1753140075.48914, "relative_end": 1753140075.48914, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::image", "start": 1753140075.489759, "relative_start": 7.493819952011108, "end": 1753140075.489759, "relative_end": 1753140075.489759, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140075.490157, "relative_start": 7.494217872619629, "end": 1753140075.490157, "relative_end": 1753140075.490157, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1c3e3c2b5f3306f2c083e21b9339fe63", "start": 1753140075.490825, "relative_start": 7.4948859214782715, "end": 1753140075.490825, "relative_end": 1753140075.490825, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::image", "start": 1753140075.491395, "relative_start": 7.495455980300903, "end": 1753140075.491395, "relative_end": 1753140075.491395, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140075.491762, "relative_start": 7.495822906494141, "end": 1753140075.491762, "relative_end": 1753140075.491762, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1c3e3c2b5f3306f2c083e21b9339fe63", "start": 1753140075.492505, "relative_start": 7.4965660572052, "end": 1753140075.492505, "relative_end": 1753140075.492505, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::image", "start": 1753140075.493226, "relative_start": 7.497287034988403, "end": 1753140075.493226, "relative_end": 1753140075.493226, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140075.49378, "relative_start": 7.497840881347656, "end": 1753140075.49378, "relative_end": 1753140075.49378, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1c3e3c2b5f3306f2c083e21b9339fe63", "start": 1753140075.494838, "relative_start": 7.498898983001709, "end": 1753140075.494838, "relative_end": 1753140075.494838, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::image", "start": 1753140075.495553, "relative_start": 7.499614000320435, "end": 1753140075.495553, "relative_end": 1753140075.495553, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140075.495986, "relative_start": 7.500046968460083, "end": 1753140075.495986, "relative_end": 1753140075.495986, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1c3e3c2b5f3306f2c083e21b9339fe63", "start": 1753140075.49667, "relative_start": 7.500730991363525, "end": 1753140075.49667, "relative_end": 1753140075.49667, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::image", "start": 1753140075.49749, "relative_start": 7.501550912857056, "end": 1753140075.49749, "relative_end": 1753140075.49749, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140075.498031, "relative_start": 7.502091884613037, "end": 1753140075.498031, "relative_end": 1753140075.498031, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1c3e3c2b5f3306f2c083e21b9339fe63", "start": 1753140075.499258, "relative_start": 7.503319025039673, "end": 1753140075.499258, "relative_end": 1753140075.499258, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::image", "start": 1753140075.500255, "relative_start": 7.504316091537476, "end": 1753140075.500255, "relative_end": 1753140075.500255, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140075.50077, "relative_start": 7.504831075668335, "end": 1753140075.50077, "relative_end": 1753140075.50077, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1c3e3c2b5f3306f2c083e21b9339fe63", "start": 1753140075.501472, "relative_start": 7.50553297996521, "end": 1753140075.501472, "relative_end": 1753140075.501472, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1753140075.501757, "relative_start": 7.505817890167236, "end": 1753140075.501757, "relative_end": 1753140075.501757, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1753140075.502261, "relative_start": 7.506321907043457, "end": 1753140075.502261, "relative_end": 1753140075.502261, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1753140075.502515, "relative_start": 7.506576061248779, "end": 1753140075.502515, "relative_end": 1753140075.502515, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1753140075.50282, "relative_start": 7.50688099861145, "end": 1753140075.50282, "relative_end": 1753140075.50282, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.custom-select", "start": 1753140075.617803, "relative_start": 7.621864080429077, "end": 1753140075.617803, "relative_end": 1753140075.617803, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1753140075.770271, "relative_start": 7.774332046508789, "end": 1753140075.770271, "relative_end": 1753140075.770271, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.custom-select", "start": 1753140075.770754, "relative_start": 7.774815082550049, "end": 1753140075.770754, "relative_end": 1753140075.770754, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1753140075.800028, "relative_start": 7.804089069366455, "end": 1753140075.800028, "relative_end": 1753140075.800028, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1753140075.800912, "relative_start": 7.804972887039185, "end": 1753140075.800912, "relative_end": 1753140075.800912, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1753140075.801276, "relative_start": 7.805336952209473, "end": 1753140075.801276, "relative_end": 1753140075.801276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.custom-select", "start": 1753140075.814207, "relative_start": 7.818268060684204, "end": 1753140075.814207, "relative_end": 1753140075.814207, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1753140075.815004, "relative_start": 7.819065093994141, "end": 1753140075.815004, "relative_end": 1753140075.815004, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.custom-select", "start": 1753140075.815422, "relative_start": 7.819483041763306, "end": 1753140075.815422, "relative_end": 1753140075.815422, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1753140075.815962, "relative_start": 7.820023059844971, "end": 1753140075.815962, "relative_end": 1753140075.815962, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1753140075.816762, "relative_start": 7.8208229541778564, "end": 1753140075.816762, "relative_end": 1753140075.816762, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1753140075.817033, "relative_start": 7.821094036102295, "end": 1753140075.817033, "relative_end": 1753140075.817033, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.custom-select", "start": 1753140075.835818, "relative_start": 7.839879035949707, "end": 1753140075.835818, "relative_end": 1753140075.835818, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1753140075.836393, "relative_start": 7.8404541015625, "end": 1753140075.836393, "relative_end": 1753140075.836393, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.custom-select", "start": 1753140075.836701, "relative_start": 7.84076189994812, "end": 1753140075.836701, "relative_end": 1753140075.836701, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1753140075.837072, "relative_start": 7.841132879257202, "end": 1753140075.837072, "relative_end": 1753140075.837072, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1753140075.837618, "relative_start": 7.841679096221924, "end": 1753140075.837618, "relative_end": 1753140075.837618, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1753140075.837881, "relative_start": 7.841942071914673, "end": 1753140075.837881, "relative_end": 1753140075.837881, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.custom-select", "start": 1753140075.858406, "relative_start": 7.862467050552368, "end": 1753140075.858406, "relative_end": 1753140075.858406, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1753140075.858886, "relative_start": 7.8629469871521, "end": 1753140075.858886, "relative_end": 1753140075.858886, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.custom-select", "start": 1753140075.859155, "relative_start": 7.863215923309326, "end": 1753140075.859155, "relative_end": 1753140075.859155, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1753140075.85951, "relative_start": 7.863570928573608, "end": 1753140075.85951, "relative_end": 1753140075.85951, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1753140075.859984, "relative_start": 7.864044904708862, "end": 1753140075.859984, "relative_end": 1753140075.859984, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1753140075.860219, "relative_start": 7.8642799854278564, "end": 1753140075.860219, "relative_end": 1753140075.860219, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::partials.fields.google-map-autocomplete-field", "start": 1753140075.860842, "relative_start": 7.864902973175049, "end": 1753140075.860842, "relative_end": 1753140075.860842, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1753140076.037807, "relative_start": 8.041867971420288, "end": 1753140076.037807, "relative_end": 1753140076.037807, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1753140076.038169, "relative_start": 8.042229890823364, "end": 1753140076.038169, "relative_end": 1753140076.038169, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1753140076.038657, "relative_start": 8.042717933654785, "end": 1753140076.038657, "relative_end": 1753140076.038657, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1753140076.038887, "relative_start": 8.042948007583618, "end": 1753140076.038887, "relative_end": 1753140076.038887, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1753140076.039843, "relative_start": 8.043904066085815, "end": 1753140076.039843, "relative_end": 1753140076.039843, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1753140076.040347, "relative_start": 8.044408082962036, "end": 1753140076.040347, "relative_end": 1753140076.040347, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1753140076.163787, "relative_start": 8.167847871780396, "end": 1753140076.163787, "relative_end": 1753140076.163787, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1753140076.164611, "relative_start": 8.16867208480835, "end": 1753140076.164611, "relative_end": 1753140076.164611, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1753140076.165054, "relative_start": 8.16911506652832, "end": 1753140076.165054, "relative_end": 1753140076.165054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1753140076.165402, "relative_start": 8.16946291923523, "end": 1753140076.165402, "relative_end": 1753140076.165402, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-form-builder::text", "start": 1753140076.166331, "relative_start": 8.170392036437988, "end": 1753140076.166331, "relative_end": 1753140076.166331, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1753140076.166958, "relative_start": 8.171019077301025, "end": 1753140076.166958, "relative_end": 1753140076.166958, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1753140076.167946, "relative_start": 8.172007083892822, "end": 1753140076.167946, "relative_end": 1753140076.167946, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-form-builder::text", "start": 1753140076.168364, "relative_start": 8.172425031661987, "end": 1753140076.168364, "relative_end": 1753140076.168364, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1753140076.168806, "relative_start": 8.172867059707642, "end": 1753140076.168806, "relative_end": 1753140076.168806, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1753140076.169382, "relative_start": 8.173443078994751, "end": 1753140076.169382, "relative_end": 1753140076.169382, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1753140076.169736, "relative_start": 8.173796892166138, "end": 1753140076.169736, "relative_end": 1753140076.169736, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1753140076.170113, "relative_start": 8.174174070358276, "end": 1753140076.170113, "relative_end": 1753140076.170113, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1753140076.170604, "relative_start": 8.174664974212646, "end": 1753140076.170604, "relative_end": 1753140076.170604, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1753140076.170837, "relative_start": 8.174897909164429, "end": 1753140076.170837, "relative_end": 1753140076.170837, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1753140076.171096, "relative_start": 8.175157070159912, "end": 1753140076.171096, "relative_end": 1753140076.171096, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1753140076.171443, "relative_start": 8.175503969192505, "end": 1753140076.171443, "relative_end": 1753140076.171443, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1753140076.172249, "relative_start": 8.176310062408447, "end": 1753140076.172249, "relative_end": 1753140076.172249, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1753140076.172761, "relative_start": 8.176821947097778, "end": 1753140076.172761, "relative_end": 1753140076.172761, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1753140076.173024, "relative_start": 8.177084922790527, "end": 1753140076.173024, "relative_end": 1753140076.173024, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1753140076.173312, "relative_start": 8.177372932434082, "end": 1753140076.173312, "relative_end": 1753140076.173312, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-form-builder::text", "start": 1753140076.173677, "relative_start": 8.177737951278687, "end": 1753140076.173677, "relative_end": 1753140076.173677, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1753140076.17414, "relative_start": 8.178200960159302, "end": 1753140076.17414, "relative_end": 1753140076.17414, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1753140076.174722, "relative_start": 8.178782939910889, "end": 1753140076.174722, "relative_end": 1753140076.174722, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-form-builder::text", "start": 1753140076.175029, "relative_start": 8.179090023040771, "end": 1753140076.175029, "relative_end": 1753140076.175029, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1753140076.175389, "relative_start": 8.179450035095215, "end": 1753140076.175389, "relative_end": 1753140076.175389, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1753140076.175891, "relative_start": 8.179951906204224, "end": 1753140076.175891, "relative_end": 1753140076.175891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-form-builder::text", "start": 1753140076.176188, "relative_start": 8.180248975753784, "end": 1753140076.176188, "relative_end": 1753140076.176188, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1753140076.176548, "relative_start": 8.180608987808228, "end": 1753140076.176548, "relative_end": 1753140076.176548, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1753140076.177048, "relative_start": 8.181108951568604, "end": 1753140076.177048, "relative_end": 1753140076.177048, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1753140076.177351, "relative_start": 8.181411981582642, "end": 1753140076.177351, "relative_end": 1753140076.177351, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1753140076.177697, "relative_start": 8.181757926940918, "end": 1753140076.177697, "relative_end": 1753140076.177697, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1753140076.178149, "relative_start": 8.182209968566895, "end": 1753140076.178149, "relative_end": 1753140076.178149, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1753140076.178372, "relative_start": 8.182432889938354, "end": 1753140076.178372, "relative_end": 1753140076.178372, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1753140076.17862, "relative_start": 8.1826810836792, "end": 1753140076.17862, "relative_end": 1753140076.17862, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1753140076.178915, "relative_start": 8.182976007461548, "end": 1753140076.178915, "relative_end": 1753140076.178915, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1753140076.179254, "relative_start": 8.18331503868103, "end": 1753140076.179254, "relative_end": 1753140076.179254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1753140076.179709, "relative_start": 8.183769941329956, "end": 1753140076.179709, "relative_end": 1753140076.179709, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1753140076.179983, "relative_start": 8.184043884277344, "end": 1753140076.179983, "relative_end": 1753140076.179983, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1753140076.180264, "relative_start": 8.184324979782104, "end": 1753140076.180264, "relative_end": 1753140076.180264, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-form-builder::text", "start": 1753140076.180568, "relative_start": 8.184628963470459, "end": 1753140076.180568, "relative_end": 1753140076.180568, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1753140076.180944, "relative_start": 8.185004949569702, "end": 1753140076.180944, "relative_end": 1753140076.180944, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1753140076.181642, "relative_start": 8.185703039169312, "end": 1753140076.181642, "relative_end": 1753140076.181642, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-form-builder::text", "start": 1753140076.18238, "relative_start": 8.18644094467163, "end": 1753140076.18238, "relative_end": 1753140076.18238, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1753140076.18305, "relative_start": 8.187110900878906, "end": 1753140076.18305, "relative_end": 1753140076.18305, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1753140076.183781, "relative_start": 8.187841892242432, "end": 1753140076.183781, "relative_end": 1753140076.183781, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.custom-select", "start": 1753140076.18427, "relative_start": 8.188330888748169, "end": 1753140076.18427, "relative_end": 1753140076.18427, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1753140076.184919, "relative_start": 8.188980102539062, "end": 1753140076.184919, "relative_end": 1753140076.184919, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.custom-select", "start": 1753140076.185263, "relative_start": 8.189323902130127, "end": 1753140076.185263, "relative_end": 1753140076.185263, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1753140076.185803, "relative_start": 8.189863920211792, "end": 1753140076.185803, "relative_end": 1753140076.185803, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1753140076.18634, "relative_start": 8.190401077270508, "end": 1753140076.18634, "relative_end": 1753140076.18634, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1753140076.186558, "relative_start": 8.190618991851807, "end": 1753140076.186558, "relative_end": 1753140076.186558, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1753140076.186805, "relative_start": 8.190865993499756, "end": 1753140076.186805, "relative_end": 1753140076.186805, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1753140076.187136, "relative_start": 8.191196918487549, "end": 1753140076.187136, "relative_end": 1753140076.187136, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1753140076.187466, "relative_start": 8.191526889801025, "end": 1753140076.187466, "relative_end": 1753140076.187466, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1753140076.187915, "relative_start": 8.191976070404053, "end": 1753140076.187915, "relative_end": 1753140076.187915, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1753140076.188123, "relative_start": 8.19218397140503, "end": 1753140076.188123, "relative_end": 1753140076.188123, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1753140076.188357, "relative_start": 8.192418098449707, "end": 1753140076.188357, "relative_end": 1753140076.188357, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1753140076.188661, "relative_start": 8.192722082138062, "end": 1753140076.188661, "relative_end": 1753140076.188661, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1753140076.188998, "relative_start": 8.193058967590332, "end": 1753140076.188998, "relative_end": 1753140076.188998, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1753140076.189491, "relative_start": 8.193552017211914, "end": 1753140076.189491, "relative_end": 1753140076.189491, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1753140076.189707, "relative_start": 8.19376802444458, "end": 1753140076.189707, "relative_end": 1753140076.189707, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1753140076.189955, "relative_start": 8.194015979766846, "end": 1753140076.189955, "relative_end": 1753140076.189955, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1753140076.190268, "relative_start": 8.194329023361206, "end": 1753140076.190268, "relative_end": 1753140076.190268, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1753140076.190591, "relative_start": 8.194652080535889, "end": 1753140076.190591, "relative_end": 1753140076.190591, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1753140076.191034, "relative_start": 8.19509506225586, "end": 1753140076.191034, "relative_end": 1753140076.191034, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1753140076.19124, "relative_start": 8.195301055908203, "end": 1753140076.19124, "relative_end": 1753140076.19124, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1753140076.191486, "relative_start": 8.195546865463257, "end": 1753140076.191486, "relative_end": 1753140076.191486, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.body.index", "start": 1753140076.191823, "relative_start": 8.195883989334106, "end": 1753140076.191823, "relative_end": 1753140076.191823, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": 1753140076.192201, "relative_start": 8.196261882781982, "end": 1753140076.192201, "relative_end": 1753140076.192201, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.meta-box", "start": 1753140076.192681, "relative_start": 8.196742057800293, "end": 1753140076.192681, "relative_end": 1753140076.192681, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": **********.488592, "relative_start": 9.***************, "end": **********.488592, "relative_end": **********.488592, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": **********.488964, "relative_start": 9.***************, "end": **********.488964, "relative_end": **********.488964, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/real-estate::partials.form-facilities", "start": **********.489278, "relative_start": 9.**************, "end": **********.489278, "relative_end": **********.489278, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.677654, "relative_start": 9.68171501159668, "end": **********.677654, "relative_end": **********.677654, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e38e2f7375f7e06238efe5faa11bb3f5", "start": **********.68004, "relative_start": 9.684100866317749, "end": **********.68004, "relative_end": **********.68004, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.body.index", "start": **********.680464, "relative_start": 9.68452501296997, "end": **********.680464, "relative_end": **********.680464, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.680742, "relative_start": 9.684803009033203, "end": **********.680742, "relative_end": **********.680742, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.meta-box", "start": **********.681142, "relative_start": 9.685203075408936, "end": **********.681142, "relative_end": **********.681142, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": **********.682027, "relative_start": 9.68608808517456, "end": **********.682027, "relative_end": **********.682027, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": **********.682366, "relative_start": 9.686426877975464, "end": **********.682366, "relative_end": **********.682366, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.body.index", "start": **********.682748, "relative_start": 9.686809062957764, "end": **********.682748, "relative_end": **********.682748, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.683054, "relative_start": 9.687114953994751, "end": **********.683054, "relative_end": **********.683054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.form-content-only", "start": **********.692839, "relative_start": 9.696899890899658, "end": **********.692839, "relative_end": **********.692839, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": **********.76803, "relative_start": 9.772090911865234, "end": **********.76803, "relative_end": **********.76803, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.768503, "relative_start": 9.772563934326172, "end": **********.768503, "relative_end": **********.768503, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.768903, "relative_start": 9.772964000701904, "end": **********.768903, "relative_end": **********.768903, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.769408, "relative_start": 9.773468971252441, "end": **********.769408, "relative_end": **********.769408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.769627, "relative_start": 9.773688077926636, "end": **********.769627, "relative_end": **********.769627, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.769884, "relative_start": 9.773945093154907, "end": **********.769884, "relative_end": **********.769884, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.textarea", "start": **********.770226, "relative_start": 9.774286985397339, "end": **********.770226, "relative_end": **********.770226, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.770606, "relative_start": 9.774667024612427, "end": **********.770606, "relative_end": **********.770606, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.770881, "relative_start": 9.77494192123413, "end": **********.770881, "relative_end": **********.770881, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.771326, "relative_start": 9.775387048721313, "end": **********.771326, "relative_end": **********.771326, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.771548, "relative_start": 9.775609016418457, "end": **********.771548, "relative_end": **********.771548, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.77179, "relative_start": 9.775851011276245, "end": **********.77179, "relative_end": **********.77179, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.media-image", "start": **********.77243, "relative_start": 9.776490926742554, "end": **********.77243, "relative_end": **********.77243, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.934252, "relative_start": 9.938313007354736, "end": **********.934252, "relative_end": **********.934252, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.image", "start": **********.934785, "relative_start": 9.938845872879028, "end": **********.934785, "relative_end": **********.934785, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.image", "start": 1753140078.060885, "relative_start": 10.064945936203003, "end": 1753140078.060885, "relative_end": 1753140078.060885, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::image", "start": 1753140078.458417, "relative_start": 10.462477922439575, "end": 1753140078.458417, "relative_end": 1753140078.458417, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1753140078.458849, "relative_start": 10.462909936904907, "end": 1753140078.458849, "relative_end": 1753140078.458849, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1c3e3c2b5f3306f2c083e21b9339fe63", "start": 1753140078.459532, "relative_start": 10.463593006134033, "end": 1753140078.459532, "relative_end": 1753140078.459532, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1753140078.459843, "relative_start": 10.463903903961182, "end": 1753140078.459843, "relative_end": 1753140078.459843, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1753140078.460303, "relative_start": 10.464364051818848, "end": 1753140078.460303, "relative_end": 1753140078.460303, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1753140078.460565, "relative_start": 10.46462607383728, "end": 1753140078.460565, "relative_end": 1753140078.460565, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1753140078.460856, "relative_start": 10.464916944503784, "end": 1753140078.460856, "relative_end": 1753140078.460856, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.custom-radio", "start": 1753140078.461315, "relative_start": 10.465375900268555, "end": 1753140078.461315, "relative_end": 1753140078.461315, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1753140078.46305, "relative_start": 10.467110872268677, "end": 1753140078.46305, "relative_end": 1753140078.46305, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.custom-radio", "start": 1753140078.463531, "relative_start": 10.467592000961304, "end": 1753140078.463531, "relative_end": 1753140078.463531, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.radio", "start": 1753140078.601682, "relative_start": 10.605742931365967, "end": 1753140078.601682, "relative_end": 1753140078.601682, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.radio", "start": 1753140078.687063, "relative_start": 10.691123962402344, "end": 1753140078.687063, "relative_end": 1753140078.687063, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1753140078.687448, "relative_start": 10.691509008407593, "end": 1753140078.687448, "relative_end": 1753140078.687448, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1753140078.687962, "relative_start": 10.692023038864136, "end": 1753140078.687962, "relative_end": 1753140078.687962, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1753140078.688226, "relative_start": 10.692286968231201, "end": 1753140078.688226, "relative_end": 1753140078.688226, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1753140078.688523, "relative_start": 10.692584037780762, "end": 1753140078.688523, "relative_end": 1753140078.688523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/seo-helper::meta-box", "start": 1753140078.689482, "relative_start": 10.693542957305908, "end": 1753140078.689482, "relative_end": 1753140078.689482, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.actions", "start": 1753140079.136129, "relative_start": 11.14018988609314, "end": 1753140079.136129, "relative_end": 1753140079.136129, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::hr", "start": 1753140079.148305, "relative_start": 11.152365922927856, "end": 1753140079.148305, "relative_end": 1753140079.148305, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::elements.meta-box-wrap", "start": 1753140079.230803, "relative_start": 11.234863996505737, "end": 1753140079.230803, "relative_end": 1753140079.230803, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": 1753140079.717999, "relative_start": 11.722059965133667, "end": 1753140079.717999, "relative_end": 1753140079.717999, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": 1753140079.718759, "relative_start": 11.722820043563843, "end": 1753140079.718759, "relative_end": 1753140079.718759, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.body.index", "start": 1753140079.719231, "relative_start": 11.723291873931885, "end": 1753140079.719231, "relative_end": 1753140079.719231, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": 1753140079.719616, "relative_start": 11.723676919937134, "end": 1753140079.719616, "relative_end": 1753140079.719616, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::elements.meta-box", "start": 1753140079.720288, "relative_start": 11.724349021911621, "end": 1753140079.720288, "relative_end": 1753140079.720288, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.form-actions", "start": 1753140079.742494, "relative_start": 11.746555089950562, "end": 1753140079.742494, "relative_end": 1753140079.742494, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": **********.278169, "relative_start": 12.282229900360107, "end": **********.278169, "relative_end": **********.278169, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": **********.278533, "relative_start": 12.282593965530396, "end": **********.278533, "relative_end": **********.278533, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.form-buttons", "start": **********.278904, "relative_start": 12.282964944839478, "end": **********.278904, "relative_end": **********.278904, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.59234, "relative_start": 12.59640097618103, "end": **********.59234, "relative_end": **********.59234, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d25e365b684c7fa9ad5ec13cfb768fc1", "start": **********.602472, "relative_start": 12.60653305053711, "end": **********.602472, "relative_end": **********.602472, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.621684, "relative_start": 12.625745058059692, "end": **********.621684, "relative_end": **********.621684, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5db12cde11beb11dd9ef0499874ec197", "start": **********.635688, "relative_start": 12.63974905014038, "end": **********.635688, "relative_end": **********.635688, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.body.index", "start": **********.659771, "relative_start": 12.663831949234009, "end": **********.659771, "relative_end": **********.659771, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.660056, "relative_start": 12.664117097854614, "end": **********.660056, "relative_end": **********.660056, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.breadcrumbs", "start": **********.662944, "relative_start": 12.667005062103271, "end": **********.662944, "relative_end": **********.662944, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.form-buttons", "start": **********.718429, "relative_start": 12.722490072250366, "end": **********.718429, "relative_end": **********.718429, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.719092, "relative_start": 12.723152875900269, "end": **********.719092, "relative_end": **********.719092, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d25e365b684c7fa9ad5ec13cfb768fc1", "start": **********.720027, "relative_start": 12.724087953567505, "end": **********.720027, "relative_end": **********.720027, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.720943, "relative_start": 12.725003957748413, "end": **********.720943, "relative_end": **********.720943, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5db12cde11beb11dd9ef0499874ec197", "start": **********.722214, "relative_start": 12.726274967193604, "end": **********.722214, "relative_end": **********.722214, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-box", "start": **********.731639, "relative_start": 12.735699892044067, "end": **********.731639, "relative_end": **********.731639, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": **********.938473, "relative_start": 12.94253396987915, "end": **********.938473, "relative_end": **********.938473, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": **********.966746, "relative_start": 12.970807075500488, "end": **********.966746, "relative_end": **********.966746, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": **********.97514, "relative_start": 12.979201078414917, "end": **********.97514, "relative_end": **********.97514, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": **********.983464, "relative_start": 12.98752498626709, "end": **********.983464, "relative_end": **********.983464, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::elements.meta-box-wrap", "start": **********.984891, "relative_start": 12.988951921463013, "end": **********.984891, "relative_end": **********.984891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": **********.98597, "relative_start": 12.990031003952026, "end": **********.98597, "relative_end": **********.98597, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": **********.986702, "relative_start": 12.990762948989868, "end": **********.986702, "relative_end": **********.986702, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.body.index", "start": **********.98702, "relative_start": 12.99108099937439, "end": **********.98702, "relative_end": **********.98702, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.987266, "relative_start": 12.991327047348022, "end": **********.987266, "relative_end": **********.987266, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::elements.meta-box", "start": **********.98765, "relative_start": 12.991710901260376, "end": **********.98765, "relative_end": **********.98765, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.987963, "relative_start": 12.992023944854736, "end": **********.987963, "relative_end": **********.987963, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": **********.988278, "relative_start": 12.99233889579773, "end": **********.988278, "relative_end": **********.988278, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": **********.988672, "relative_start": 12.992733001708984, "end": **********.988672, "relative_end": **********.988672, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.custom-radio", "start": **********.988981, "relative_start": 12.9930419921875, "end": **********.988981, "relative_end": **********.988981, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.custom-radio", "start": **********.989573, "relative_start": 12.99363398551941, "end": **********.989573, "relative_end": **********.989573, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.radio", "start": **********.990157, "relative_start": 12.994217872619629, "end": **********.990157, "relative_end": **********.990157, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.radio", "start": **********.990772, "relative_start": 12.994832992553711, "end": **********.990772, "relative_end": **********.990772, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.991236, "relative_start": 12.995296955108643, "end": **********.991236, "relative_end": **********.991236, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.9919, "relative_start": 12.99596095085144, "end": **********.9919, "relative_end": **********.9919, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.992194, "relative_start": 12.996254920959473, "end": **********.992194, "relative_end": **********.992194, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.992531, "relative_start": 12.996592044830322, "end": **********.992531, "relative_end": **********.992531, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.992886, "relative_start": 12.996947050094604, "end": **********.992886, "relative_end": **********.992886, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.993356, "relative_start": 12.997416973114014, "end": **********.993356, "relative_end": **********.993356, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": **********.993668, "relative_start": 12.997729063034058, "end": **********.993668, "relative_end": **********.993668, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": **********.994102, "relative_start": 12.998162984848022, "end": **********.994102, "relative_end": **********.994102, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": **********.99455, "relative_start": 12.998610973358154, "end": **********.99455, "relative_end": **********.99455, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.99527, "relative_start": 12.999330997467041, "end": **********.99527, "relative_end": **********.99527, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.996045, "relative_start": 13.0001060962677, "end": **********.996045, "relative_end": **********.996045, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.996384, "relative_start": 13.000444889068604, "end": **********.996384, "relative_end": **********.996384, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.996762, "relative_start": 13.000823020935059, "end": **********.996762, "relative_end": **********.996762, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.997166, "relative_start": 13.001226902008057, "end": **********.997166, "relative_end": **********.997166, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.997759, "relative_start": 13.001820087432861, "end": **********.997759, "relative_end": **********.997759, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": **********.998102, "relative_start": 13.00216293334961, "end": **********.998102, "relative_end": **********.998102, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": **********.998515, "relative_start": 13.002575874328613, "end": **********.998515, "relative_end": **********.998515, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.autocomplete", "start": **********.99911, "relative_start": 13.00317096710205, "end": **********.99911, "relative_end": **********.99911, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.autocomplete", "start": **********.192256, "relative_start": 13.196316957473755, "end": **********.192256, "relative_end": **********.192256, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.custom-select", "start": **********.227326, "relative_start": 13.23138689994812, "end": **********.227326, "relative_end": **********.227326, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.227745, "relative_start": 13.23180603981018, "end": **********.227745, "relative_end": **********.227745, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.228575, "relative_start": 13.232635974884033, "end": **********.228575, "relative_end": **********.228575, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.228945, "relative_start": 13.233006000518799, "end": **********.228945, "relative_end": **********.228945, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.229336, "relative_start": 13.233397006988525, "end": **********.229336, "relative_end": **********.229336, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.229733, "relative_start": 13.23379397392273, "end": **********.229733, "relative_end": **********.229733, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::elements.meta-box", "start": **********.231738, "relative_start": 13.235799074172974, "end": **********.231738, "relative_end": **********.231738, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": **********.301759, "relative_start": 13.305819988250732, "end": **********.301759, "relative_end": **********.301759, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.master", "start": **********.303499, "relative_start": 13.307559967041016, "end": **********.303499, "relative_end": **********.303499, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.before-content", "start": **********.304615, "relative_start": 13.30867600440979, "end": **********.304615, "relative_end": **********.304615, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.header", "start": **********.305053, "relative_start": 13.3091139793396, "end": **********.305053, "relative_end": **********.305053, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::133aa97c11fca0f84f02ebcb9fd067dc", "start": **********.306949, "relative_start": 13.311009883880615, "end": **********.306949, "relative_end": **********.306949, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.logo", "start": **********.307331, "relative_start": 13.311392068862915, "end": **********.307331, "relative_end": **********.307331, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::global-search.navbar-input", "start": **********.307936, "relative_start": 13.311996936798096, "end": **********.307936, "relative_end": **********.307936, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.text-input", "start": **********.308599, "relative_start": 13.312659978866577, "end": **********.308599, "relative_end": **********.308599, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.31, "relative_start": 13.314060926437378, "end": **********.31, "relative_end": **********.31, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.error", "start": **********.310565, "relative_start": 13.314625978469849, "end": **********.310565, "relative_end": **********.310565, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": **********.310992, "relative_start": 13.31505298614502, "end": **********.310992, "relative_end": **********.310992, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.312026, "relative_start": 13.316087007522583, "end": **********.312026, "relative_end": **********.312026, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dc988bb05638c97d86c3bc8a9b727e31", "start": **********.3136, "relative_start": 13.317661046981812, "end": **********.3136, "relative_end": **********.3136, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.theme-toggle", "start": **********.313963, "relative_start": 13.318023920059204, "end": **********.313963, "relative_end": **********.313963, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ec711b4f47c4b42ebc81a7564c1d8d33", "start": **********.315917, "relative_start": 13.31997799873352, "end": **********.315917, "relative_end": **********.315917, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::notification.nav-item", "start": **********.317507, "relative_start": 13.321568012237549, "end": **********.317507, "relative_end": **********.317507, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::306e03d3dc5634ee2e82192f553c6f9b", "start": **********.318857, "relative_start": 13.322917938232422, "end": **********.318857, "relative_end": **********.318857, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.user-menu", "start": **********.324444, "relative_start": 13.328505039215088, "end": **********.324444, "relative_end": **********.324444, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown.item", "start": **********.32987, "relative_start": 13.333930969238281, "end": **********.32987, "relative_end": **********.32987, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2907df26a6102c24ab0c37391217b338", "start": **********.33131, "relative_start": 13.335371017456055, "end": **********.33131, "relative_end": **********.33131, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown.item", "start": **********.331726, "relative_start": 13.335787057876587, "end": **********.331726, "relative_end": **********.331726, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f38bffca7b8a1a50e97a6950ffd66c5c", "start": **********.333, "relative_start": 13.337060928344727, "end": **********.333, "relative_end": **********.333, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown.index", "start": **********.333278, "relative_start": 13.337338924407959, "end": **********.333278, "relative_end": **********.333278, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.aside", "start": **********.333989, "relative_start": 13.33804988861084, "end": **********.333989, "relative_end": **********.333989, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::133aa97c11fca0f84f02ebcb9fd067dc", "start": **********.335039, "relative_start": 13.339099884033203, "end": **********.335039, "relative_end": **********.335039, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.logo", "start": **********.335288, "relative_start": 13.339349031448364, "end": **********.335288, "relative_end": **********.335288, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown.item", "start": **********.337645, "relative_start": 13.341706037521362, "end": **********.337645, "relative_end": **********.337645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2907df26a6102c24ab0c37391217b338", "start": **********.338637, "relative_start": 13.342698097229004, "end": **********.338637, "relative_end": **********.338637, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown.item", "start": **********.339218, "relative_start": 13.343278884887695, "end": **********.339218, "relative_end": **********.339218, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f38bffca7b8a1a50e97a6950ffd66c5c", "start": **********.340302, "relative_start": 13.34436297416687, "end": **********.340302, "relative_end": **********.340302, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown.index", "start": **********.340676, "relative_start": 13.34473705291748, "end": **********.340676, "relative_end": **********.340676, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.sidebar", "start": **********.341601, "relative_start": 13.345661878585815, "end": **********.341601, "relative_end": **********.341601, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav", "start": **********.342101, "relative_start": 13.34616208076477, "end": **********.342101, "relative_end": **********.342101, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.364731, "relative_start": 13.368792057037354, "end": **********.364731, "relative_end": **********.364731, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.366342, "relative_start": 13.370403051376343, "end": **********.366342, "relative_end": **********.366342, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::af68cda57c5ca67f3b8a7729953880bc", "start": **********.369325, "relative_start": 13.373385906219482, "end": **********.369325, "relative_end": **********.369325, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.370299, "relative_start": 13.374360084533691, "end": **********.370299, "relative_end": **********.370299, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.371801, "relative_start": 13.375861883163452, "end": **********.371801, "relative_end": **********.371801, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5def649a5a47936dda7e47db6ffcfa75", "start": **********.374155, "relative_start": 13.378216028213501, "end": **********.374155, "relative_end": **********.374155, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.374707, "relative_start": 13.378767967224121, "end": **********.374707, "relative_end": **********.374707, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.376042, "relative_start": 13.38010287284851, "end": **********.376042, "relative_end": **********.376042, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.376538, "relative_start": 13.380599021911621, "end": **********.376538, "relative_end": **********.376538, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.377377, "relative_start": 13.38143801689148, "end": **********.377377, "relative_end": **********.377377, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.377842, "relative_start": 13.381902933120728, "end": **********.377842, "relative_end": **********.377842, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.379022, "relative_start": 13.383082866668701, "end": **********.379022, "relative_end": **********.379022, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.37948, "relative_start": 13.383540868759155, "end": **********.37948, "relative_end": **********.37948, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.380651, "relative_start": 13.384711980819702, "end": **********.380651, "relative_end": **********.380651, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.381102, "relative_start": 13.385163068771362, "end": **********.381102, "relative_end": **********.381102, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.382206, "relative_start": 13.386266946792603, "end": **********.382206, "relative_end": **********.382206, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.382655, "relative_start": 13.38671588897705, "end": **********.382655, "relative_end": **********.382655, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.383436, "relative_start": 13.387496948242188, "end": **********.383436, "relative_end": **********.383436, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.384148, "relative_start": 13.388208866119385, "end": **********.384148, "relative_end": **********.384148, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.385806, "relative_start": 13.389867067337036, "end": **********.385806, "relative_end": **********.385806, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.386437, "relative_start": 13.390497922897339, "end": **********.386437, "relative_end": **********.386437, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.387431, "relative_start": 13.391491889953613, "end": **********.387431, "relative_end": **********.387431, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dd4c2087b0a47210b5b4e3ee87ef3eca", "start": **********.38982, "relative_start": 13.39388108253479, "end": **********.38982, "relative_end": **********.38982, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.390789, "relative_start": 13.394850015640259, "end": **********.390789, "relative_end": **********.390789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.391918, "relative_start": 13.395978927612305, "end": **********.391918, "relative_end": **********.391918, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ca20cd1247722214b06db9aa7c493b27", "start": **********.394978, "relative_start": 13.399039030075073, "end": **********.394978, "relative_end": **********.394978, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.395932, "relative_start": 13.399992942810059, "end": **********.395932, "relative_end": **********.395932, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navbar.badge-count", "start": **********.39657, "relative_start": 13.400630950927734, "end": **********.39657, "relative_end": **********.39657, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.397299, "relative_start": 13.401360034942627, "end": **********.397299, "relative_end": **********.397299, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.398693, "relative_start": 13.402754068374634, "end": **********.398693, "relative_end": **********.398693, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.399435, "relative_start": 13.403496026992798, "end": **********.399435, "relative_end": **********.399435, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navbar.badge-count", "start": **********.399917, "relative_start": 13.403977870941162, "end": **********.399917, "relative_end": **********.399917, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.400543, "relative_start": 13.404603958129883, "end": **********.400543, "relative_end": **********.400543, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.401862, "relative_start": 13.405922889709473, "end": **********.401862, "relative_end": **********.401862, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.402602, "relative_start": 13.406662940979004, "end": **********.402602, "relative_end": **********.402602, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.404065, "relative_start": 13.408125877380371, "end": **********.404065, "relative_end": **********.404065, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.404821, "relative_start": 13.408881902694702, "end": **********.404821, "relative_end": **********.404821, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.405745, "relative_start": 13.4098060131073, "end": **********.405745, "relative_end": **********.405745, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a3acd3bd206d0793e18d491720de886c", "start": **********.40761, "relative_start": 13.411670923233032, "end": **********.40761, "relative_end": **********.40761, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.408104, "relative_start": 13.41216492652893, "end": **********.408104, "relative_end": **********.408104, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.408717, "relative_start": 13.4127779006958, "end": **********.408717, "relative_end": **********.408717, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::fd978891e1ac33723cbffddc6658659a", "start": **********.410885, "relative_start": 13.41494607925415, "end": **********.410885, "relative_end": **********.410885, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.411494, "relative_start": 13.415555000305176, "end": **********.411494, "relative_end": **********.411494, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.412563, "relative_start": 13.416624069213867, "end": **********.412563, "relative_end": **********.412563, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.4131, "relative_start": 13.417160987854004, "end": **********.4131, "relative_end": **********.4131, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.414088, "relative_start": 13.4181489944458, "end": **********.414088, "relative_end": **********.414088, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.414637, "relative_start": 13.418698072433472, "end": **********.414637, "relative_end": **********.414637, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.415798, "relative_start": 13.419858932495117, "end": **********.415798, "relative_end": **********.415798, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::13365b7e5a448d13150fdb4b3884b510", "start": **********.418232, "relative_start": 13.422292947769165, "end": **********.418232, "relative_end": **********.418232, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.419242, "relative_start": 13.42330288887024, "end": **********.419242, "relative_end": **********.419242, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.420555, "relative_start": 13.42461609840393, "end": **********.420555, "relative_end": **********.420555, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::98e88d58787b8dfeb6f0d1dc0a785cfd", "start": **********.423572, "relative_start": 13.427633047103882, "end": **********.423572, "relative_end": **********.423572, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.424049, "relative_start": 13.428109884262085, "end": **********.424049, "relative_end": **********.424049, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navbar.badge-count", "start": **********.424396, "relative_start": 13.428457021713257, "end": **********.424396, "relative_end": **********.424396, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.424824, "relative_start": 13.428884983062744, "end": **********.424824, "relative_end": **********.424824, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::cbce7a4c13e13a70eabb7759894a60fd", "start": **********.426213, "relative_start": 13.43027400970459, "end": **********.426213, "relative_end": **********.426213, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.426692, "relative_start": 13.430752992630005, "end": **********.426692, "relative_end": **********.426692, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::fe6fcb7551f99a7d9d1c5a4c0011f471", "start": **********.428252, "relative_start": 13.432312965393066, "end": **********.428252, "relative_end": **********.428252, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.428728, "relative_start": 13.432789087295532, "end": **********.428728, "relative_end": **********.428728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.42933, "relative_start": 13.433391094207764, "end": **********.42933, "relative_end": **********.42933, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::351bdfbe842eff22e08f1df9d5f5beb1", "start": **********.430985, "relative_start": 13.435045957565308, "end": **********.430985, "relative_end": **********.430985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.431489, "relative_start": 13.435549974441528, "end": **********.431489, "relative_end": **********.431489, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.432369, "relative_start": 13.436429977416992, "end": **********.432369, "relative_end": **********.432369, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.432853, "relative_start": 13.436913967132568, "end": **********.432853, "relative_end": **********.432853, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.434153, "relative_start": 13.43821406364441, "end": **********.434153, "relative_end": **********.434153, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.434672, "relative_start": 13.438733100891113, "end": **********.434672, "relative_end": **********.434672, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.435358, "relative_start": 13.439419031143188, "end": **********.435358, "relative_end": **********.435358, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::98e88d58787b8dfeb6f0d1dc0a785cfd", "start": **********.437289, "relative_start": 13.441349983215332, "end": **********.437289, "relative_end": **********.437289, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.438168, "relative_start": 13.44222903251648, "end": **********.438168, "relative_end": **********.438168, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.439162, "relative_start": 13.443222999572754, "end": **********.439162, "relative_end": **********.439162, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0343a1b0800146d7d9cf6a9514ec7bf4", "start": **********.441582, "relative_start": 13.445642948150635, "end": **********.441582, "relative_end": **********.441582, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.442477, "relative_start": 13.446537971496582, "end": **********.442477, "relative_end": **********.442477, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::15422be7d0f2aaf8c8244c1e9db20ad9", "start": **********.445298, "relative_start": 13.449358940124512, "end": **********.445298, "relative_end": **********.445298, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.446217, "relative_start": 13.450278043746948, "end": **********.446217, "relative_end": **********.446217, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2e5e965add6d0ee3aadb02ca38e70825", "start": **********.448461, "relative_start": 13.452522039413452, "end": **********.448461, "relative_end": **********.448461, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.449002, "relative_start": 13.453063011169434, "end": **********.449002, "relative_end": **********.449002, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5b8d99843e0f8eff6046d7236026b187", "start": **********.450217, "relative_start": 13.454277992248535, "end": **********.450217, "relative_end": **********.450217, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.450704, "relative_start": 13.45476508140564, "end": **********.450704, "relative_end": **********.450704, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dd248739db8ba923ebfe1f426bb71a38", "start": **********.451903, "relative_start": 13.455964088439941, "end": **********.451903, "relative_end": **********.451903, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.452338, "relative_start": 13.456398963928223, "end": **********.452338, "relative_end": **********.452338, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d6f928aaf1e585d3246cb3bee8fdd45", "start": **********.454632, "relative_start": 13.458693027496338, "end": **********.454632, "relative_end": **********.454632, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.455568, "relative_start": 13.45962905883789, "end": **********.455568, "relative_end": **********.455568, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::acbc3e3e19c70a7ebb0e7a5d98d84fbe", "start": **********.458424, "relative_start": 13.462485074996948, "end": **********.458424, "relative_end": **********.458424, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.459375, "relative_start": 13.463435888290405, "end": **********.459375, "relative_end": **********.459375, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.460546, "relative_start": 13.464607000350952, "end": **********.460546, "relative_end": **********.460546, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6e0b6ed9bf49c6ad9d02af2c7f911103", "start": **********.46291, "relative_start": 13.466970920562744, "end": **********.46291, "relative_end": **********.46291, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.463857, "relative_start": 13.467917919158936, "end": **********.463857, "relative_end": **********.463857, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.465021, "relative_start": 13.46908187866211, "end": **********.465021, "relative_end": **********.465021, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dac323985d9d2618ad252313442aaf03", "start": **********.468216, "relative_start": 13.47227692604065, "end": **********.468216, "relative_end": **********.468216, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.469165, "relative_start": 13.473226070404053, "end": **********.469165, "relative_end": **********.469165, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::298cd8a12b86a6f371ff06491a0822fa", "start": **********.472023, "relative_start": 13.476083993911743, "end": **********.472023, "relative_end": **********.472023, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.473108, "relative_start": 13.477169036865234, "end": **********.473108, "relative_end": **********.473108, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c59870a61b233f0766e3260625bdb025", "start": **********.475594, "relative_start": 13.479655027389526, "end": **********.475594, "relative_end": **********.475594, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.476548, "relative_start": 13.480608940124512, "end": **********.476548, "relative_end": **********.476548, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3890eca5d46a147ef99ddf994453d2ec", "start": **********.478883, "relative_start": 13.482944011688232, "end": **********.478883, "relative_end": **********.478883, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.479811, "relative_start": 13.483871936798096, "end": **********.479811, "relative_end": **********.479811, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ff1fde71531b073b2c658173537aaea5", "start": **********.482195, "relative_start": 13.486255884170532, "end": **********.482195, "relative_end": **********.482195, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.483072, "relative_start": 13.487133026123047, "end": **********.483072, "relative_end": **********.483072, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5cef0de51e1489c31c7fcb5d7f2f6a97", "start": **********.485254, "relative_start": 13.489315032958984, "end": **********.485254, "relative_end": **********.485254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.485945, "relative_start": 13.49000597000122, "end": **********.485945, "relative_end": **********.485945, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::89cb89d3fdb0a0a12f8aa61073c231e6", "start": **********.488224, "relative_start": 13.492285013198853, "end": **********.488224, "relative_end": **********.488224, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.488783, "relative_start": 13.492843866348267, "end": **********.488783, "relative_end": **********.488783, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5a5b09d3f2ee0ddb2b536feb532d230a", "start": **********.490248, "relative_start": 13.494308948516846, "end": **********.490248, "relative_end": **********.490248, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.490744, "relative_start": 13.494805097579956, "end": **********.490744, "relative_end": **********.490744, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::fedc652debeb23dcbb31a98830baa397", "start": **********.491985, "relative_start": 13.49604606628418, "end": **********.491985, "relative_end": **********.491985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.492495, "relative_start": 13.496556043624878, "end": **********.492495, "relative_end": **********.492495, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.493111, "relative_start": 13.497171878814697, "end": **********.493111, "relative_end": **********.493111, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ff3b2cf4e42e74e63db76ff05c5f2374", "start": **********.495468, "relative_start": 13.499528884887695, "end": **********.495468, "relative_end": **********.495468, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.496297, "relative_start": 13.500357866287231, "end": **********.496297, "relative_end": **********.496297, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.497334, "relative_start": 13.501394987106323, "end": **********.497334, "relative_end": **********.497334, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::745871da7c635a3f461dfaeeef54a48e", "start": **********.498728, "relative_start": 13.50278902053833, "end": **********.498728, "relative_end": **********.498728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.499195, "relative_start": 13.50325608253479, "end": **********.499195, "relative_end": **********.499195, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d6f928aaf1e585d3246cb3bee8fdd45", "start": **********.500354, "relative_start": 13.504415035247803, "end": **********.500354, "relative_end": **********.500354, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.50078, "relative_start": 13.504841089248657, "end": **********.50078, "relative_end": **********.50078, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.501344, "relative_start": 13.505404949188232, "end": **********.501344, "relative_end": **********.501344, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2b3233eda7e50501ef45fd875b12da49", "start": **********.502446, "relative_start": 13.50650691986084, "end": **********.502446, "relative_end": **********.502446, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.502872, "relative_start": 13.506932973861694, "end": **********.502872, "relative_end": **********.502872, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.5037, "relative_start": 13.507761001586914, "end": **********.5037, "relative_end": **********.5037, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b13663c834a4ae876ef8f72aa0610e8c", "start": **********.505556, "relative_start": 13.50961709022522, "end": **********.505556, "relative_end": **********.505556, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.page-header", "start": **********.506429, "relative_start": 13.51048994064331, "end": **********.506429, "relative_end": **********.506429, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::breadcrumb", "start": **********.506791, "relative_start": 13.510852098464966, "end": **********.506791, "relative_end": **********.506791, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.footer", "start": **********.507612, "relative_start": 13.511672973632812, "end": **********.507612, "relative_end": **********.507612, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.copyright", "start": **********.507946, "relative_start": 13.512006998062134, "end": **********.507946, "relative_end": **********.507946, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.after-content", "start": **********.508924, "relative_start": 13.512984991073608, "end": **********.508924, "relative_end": **********.508924, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::global-search.form", "start": **********.509762, "relative_start": 13.51382303237915, "end": **********.509762, "relative_end": **********.509762, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3cec1c87224222bda738c53f782c5bc1", "start": **********.511458, "relative_start": 13.5155189037323, "end": **********.511458, "relative_end": **********.511458, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.text-input", "start": **********.511762, "relative_start": 13.515822887420654, "end": **********.511762, "relative_end": **********.511762, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.513166, "relative_start": 13.517226934432983, "end": **********.513166, "relative_end": **********.513166, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.error", "start": **********.513724, "relative_start": 13.51778507232666, "end": **********.513724, "relative_end": **********.513724, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": **********.514156, "relative_start": 13.518217086791992, "end": **********.514156, "relative_end": **********.514156, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.index", "start": **********.514598, "relative_start": 13.518658876419067, "end": **********.514598, "relative_end": **********.514598, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::414e4e803eeec6389552bb46515583c5", "start": **********.515741, "relative_start": 13.51980209350586, "end": **********.515741, "relative_end": **********.515741, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c47a448d99d5719cb034f7947c739ff8", "start": **********.516668, "relative_start": 13.520729064941406, "end": **********.516668, "relative_end": **********.516668, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e226165e1fca7eccb10f6857d7cd235a", "start": **********.517525, "relative_start": 13.521585941314697, "end": **********.517525, "relative_end": **********.517525, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal", "start": **********.517879, "relative_start": 13.521939992904663, "end": **********.517879, "relative_end": **********.517879, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::custom-template", "start": **********.518953, "relative_start": 13.523014068603516, "end": **********.518953, "relative_end": **********.518953, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/media::partials.media", "start": **********.519323, "relative_start": 13.523384094238281, "end": **********.519323, "relative_end": **********.519323, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close-button", "start": **********.520207, "relative_start": 13.52426791191101, "end": **********.520207, "relative_end": **********.520207, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::loading", "start": **********.52074, "relative_start": 13.524801015853882, "end": **********.52074, "relative_end": **********.52074, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.text-input", "start": **********.521309, "relative_start": 13.525369882583618, "end": **********.521309, "relative_end": **********.521309, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.522209, "relative_start": 13.526269912719727, "end": **********.522209, "relative_end": **********.522209, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.error", "start": **********.522581, "relative_start": 13.526642084121704, "end": **********.522581, "relative_end": **********.522581, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": **********.522882, "relative_start": 13.52694296836853, "end": **********.522882, "relative_end": **********.522882, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.523217, "relative_start": 13.527277946472168, "end": **********.523217, "relative_end": **********.523217, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.524236, "relative_start": 13.528296947479248, "end": **********.524236, "relative_end": **********.524236, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.524866, "relative_start": 13.528927087783813, "end": **********.524866, "relative_end": **********.524866, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal", "start": **********.525627, "relative_start": 13.529687881469727, "end": **********.525627, "relative_end": **********.525627, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close-button", "start": **********.526906, "relative_start": 13.530966997146606, "end": **********.526906, "relative_end": **********.526906, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/media::config", "start": **********.527495, "relative_start": 13.531555891036987, "end": **********.527495, "relative_end": **********.527495, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::debug-badge", "start": **********.65608, "relative_start": 13.660140991210938, "end": **********.65608, "relative_end": **********.65608, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.action", "start": **********.656991, "relative_start": 13.661051988601685, "end": **********.656991, "relative_end": **********.656991, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.alert", "start": **********.658008, "relative_start": 13.662069082260132, "end": **********.658008, "relative_end": **********.658008, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close-button", "start": **********.658927, "relative_start": 13.66298794746399, "end": **********.658927, "relative_end": **********.658927, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dcf17957c4aa053a618fd9c312cc29fc", "start": **********.659716, "relative_start": 13.663776874542236, "end": **********.659716, "relative_end": **********.659716, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal", "start": **********.660011, "relative_start": 13.664072036743164, "end": **********.660011, "relative_end": **********.660011, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.action", "start": **********.661209, "relative_start": 13.66527009010315, "end": **********.661209, "relative_end": **********.661209, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.alert", "start": **********.662021, "relative_start": 13.66608190536499, "end": **********.662021, "relative_end": **********.662021, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close-button", "start": **********.662798, "relative_start": 13.666858911514282, "end": **********.662798, "relative_end": **********.662798, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::fcfb9f9ba5fb460899f38b71f491e1fe", "start": **********.663731, "relative_start": 13.667792081832886, "end": **********.663731, "relative_end": **********.663731, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal", "start": **********.664024, "relative_start": 13.668085098266602, "end": **********.664024, "relative_end": **********.664024, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::layouts.base", "start": **********.665271, "relative_start": 13.669332027435303, "end": **********.665271, "relative_end": **********.665271, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.layouts.header", "start": **********.666723, "relative_start": 13.670783996582031, "end": **********.666723, "relative_end": **********.666723, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: assets::header", "start": **********.671516, "relative_start": 13.67557692527771, "end": **********.671516, "relative_end": **********.671516, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::elements.common", "start": **********.674499, "relative_start": 13.678560018539429, "end": **********.674499, "relative_end": **********.674499, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: assets::footer", "start": **********.676912, "relative_start": 13.680973052978516, "end": **********.676912, "relative_end": **********.676912, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::notification.notification", "start": **********.678181, "relative_start": 13.682241916656494, "end": **********.678181, "relative_end": **********.678181, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.67956, "relative_start": 13.683620929718018, "end": **********.680485, "relative_end": **********.680485, "duration": 0.0009250640869140625, "duration_str": "925μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 76019024, "peak_usage_str": "72MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "xmetr.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 679, "nb_templates": 679, "templates": [{"name": "1x plugins/real-estate::partials.form-features", "param_count": null, "params": [], "start": **********.937515, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/real-estate/resources/views/partials/form-features.blade.phpplugins/real-estate::partials.form-features", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fresources%2Fviews%2Fpartials%2Fform-features.blade.php:1", "ajax": false, "filename": "form-features.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/real-estate::partials.form-features"}, {"name": "22x ********************************::form.checkbox", "param_count": null, "params": [], "start": **********.082724, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 22, "name_original": "********************************::form.checkbox"}, {"name": "1x core/base::forms.form", "param_count": null, "params": [], "start": **********.187321, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/form.blade.phpcore/base::forms.form", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fform.blade.php:1", "ajax": false, "filename": "form.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.form"}, {"name": "1x plugins/language::partials.notification", "param_count": null, "params": [], "start": **********.91414, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/language/resources/views/partials/notification.blade.phpplugins/language::partials.notification", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage%2Fresources%2Fviews%2Fpartials%2Fnotification.blade.php:1", "ajax": false, "filename": "notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/language::partials.notification"}, {"name": "1x ********************************::alert", "param_count": null, "params": [], "start": **********.054777, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/alert.blade.php********************************::alert", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Falert.blade.php:1", "ajax": false, "filename": "alert.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::alert"}, {"name": "1x __components::920a4a8abfd99078c24a8f8db36a19f4", "param_count": null, "params": [], "start": **********.250108, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/920a4a8abfd99078c24a8f8db36a19f4.blade.php__components::920a4a8abfd99078c24a8f8db36a19f4", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F920a4a8abfd99078c24a8f8db36a19f4.blade.php:1", "ajax": false, "filename": "920a4a8abfd99078c24a8f8db36a19f4.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::920a4a8abfd99078c24a8f8db36a19f4"}, {"name": "3x core/base::forms.fields.text", "param_count": null, "params": [], "start": **********.268786, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/text.blade.phpcore/base::forms.fields.text", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Ftext.blade.php:1", "ajax": false, "filename": "text.blade.php", "line": "?"}, "render_count": 3, "name_original": "core/base::forms.fields.text"}, {"name": "24x core/base::forms.partials.label", "param_count": null, "params": [], "start": **********.410053, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/label.blade.phpcore/base::forms.partials.label", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Flabel.blade.php:1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 24, "name_original": "core/base::forms.partials.label"}, {"name": "26x ********************************::form.field", "param_count": null, "params": [], "start": **********.410804, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/field.blade.php********************************::form.field", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ffield.blade.php:1", "ajax": false, "filename": "field.blade.php", "line": "?"}, "render_count": 26, "name_original": "********************************::form.field"}, {"name": "26x core/base::forms.partials.help-block", "param_count": null, "params": [], "start": **********.413253, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/help-block.blade.phpcore/base::forms.partials.help-block", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fhelp-block.blade.php:1", "ajax": false, "filename": "help-block.blade.php", "line": "?"}, "render_count": 26, "name_original": "core/base::forms.partials.help-block"}, {"name": "26x core/base::forms.partials.errors", "param_count": null, "params": [], "start": **********.472883, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/errors.blade.phpcore/base::forms.partials.errors", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Ferrors.blade.php:1", "ajax": false, "filename": "errors.blade.php", "line": "?"}, "render_count": 26, "name_original": "core/base::forms.partials.errors"}, {"name": "30x core/base::forms.columns.column-span", "param_count": null, "params": [], "start": **********.524355, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/columns/column-span.blade.phpcore/base::forms.columns.column-span", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fcolumns%2Fcolumn-span.blade.php:1", "ajax": false, "filename": "column-span.blade.php", "line": "?"}, "render_count": 30, "name_original": "core/base::forms.columns.column-span"}, {"name": "1x packages/slug::forms.fields.permalink", "param_count": null, "params": [], "start": **********.527506, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/packages/slug/resources/views/forms/fields/permalink.blade.phppackages/slug::forms.fields.permalink", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fslug%2Fresources%2Fviews%2Fforms%2Ffields%2Fpermalink.blade.php:1", "ajax": false, "filename": "permalink.blade.php", "line": "?"}, "render_count": 1, "name_original": "packages/slug::forms.fields.permalink"}, {"name": "1x packages/slug::permalink", "param_count": null, "params": [], "start": **********.601271, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/packages/slug/resources/views/permalink.blade.phppackages/slug::permalink", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fslug%2Fresources%2Fviews%2Fpermalink.blade.php:1", "ajax": false, "filename": "permalink.blade.php", "line": "?"}, "render_count": 1, "name_original": "packages/slug::permalink"}, {"name": "1x __components::0dc66e2ea833d19323a8163033ac48c1", "param_count": null, "params": [], "start": 1753140071.119231, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/0dc66e2ea833d19323a8163033ac48c1.blade.php__components::0dc66e2ea833d19323a8163033ac48c1", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F0dc66e2ea833d19323a8163033ac48c1.blade.php:1", "ajax": false, "filename": "0dc66e2ea833d19323a8163033ac48c1.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0dc66e2ea833d19323a8163033ac48c1"}, {"name": "5x ********************************::form.text-input", "param_count": null, "params": [], "start": 1753140071.137589, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/text-input.blade.php********************************::form.text-input", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ftext-input.blade.php:1", "ajax": false, "filename": "text-input.blade.php", "line": "?"}, "render_count": 5, "name_original": "********************************::form.text-input"}, {"name": "4x ********************************::form.label", "param_count": null, "params": [], "start": 1753140071.138735, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/label.blade.php********************************::form.label", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Flabel.blade.php:1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::form.label"}, {"name": "5x ********************************::form.error", "param_count": null, "params": [], "start": 1753140071.139227, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/error.blade.php********************************::form.error", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ferror.blade.php:1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 5, "name_original": "********************************::form.error"}, {"name": "5x ********************************::form-group", "param_count": null, "params": [], "start": 1753140071.139593, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form-group.blade.php********************************::form-group", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform-group.blade.php:1", "ajax": false, "filename": "form-group.blade.php", "line": "?"}, "render_count": 5, "name_original": "********************************::form-group"}, {"name": "1x ********************************::form.helper-text", "param_count": null, "params": [], "start": 1753140071.140123, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/helper-text.blade.php********************************::form.helper-text", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fhelper-text.blade.php:1", "ajax": false, "filename": "helper-text.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::form.helper-text"}, {"name": "1x core/base::forms.partials.error", "param_count": null, "params": [], "start": 1753140071.170423, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/error.blade.phpcore/base::forms.partials.error", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Ferror.blade.php:1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.partials.error"}, {"name": "2x core/base::forms.fields.textarea", "param_count": null, "params": [], "start": 1753140071.195305, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/textarea.blade.phpcore/base::forms.fields.textarea", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Ftextarea.blade.php:1", "ajax": false, "filename": "textarea.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::forms.fields.textarea"}, {"name": "1x core/base::forms.fields.on-off", "param_count": null, "params": [], "start": 1753140071.356429, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/on-off.blade.phpcore/base::forms.fields.on-off", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fon-off.blade.php:1", "ajax": false, "filename": "on-off.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.on-off"}, {"name": "1x core/base::forms.partials.on-off", "param_count": null, "params": [], "start": 1753140071.526966, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/on-off.blade.phpcore/base::forms.partials.on-off", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fon-off.blade.php:1", "ajax": false, "filename": "on-off.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.partials.on-off"}, {"name": "1x ********************************::form.toggle", "param_count": null, "params": [], "start": 1753140071.691317, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/toggle.blade.php********************************::form.toggle", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ftoggle.blade.php:1", "ajax": false, "filename": "toggle.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::form.toggle"}, {"name": "1x core/base::forms.fields.editor", "param_count": null, "params": [], "start": 1753140071.69424, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/editor.blade.phpcore/base::forms.fields.editor", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Feditor.blade.php:1", "ajax": false, "filename": "editor.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.editor"}, {"name": "1x core/base::forms.partials.editor", "param_count": null, "params": [], "start": 1753140071.893462, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/editor.blade.phpcore/base::forms.partials.editor", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Feditor.blade.php:1", "ajax": false, "filename": "editor.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.partials.editor"}, {"name": "55x ********************************::button", "param_count": null, "params": [], "start": 1753140072.233614, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/button.blade.php********************************::button", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fbutton.blade.php:1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 55, "name_original": "********************************::button"}, {"name": "1x __components::7b4681f3a79c415079e436b7e5e9f3ac", "param_count": null, "params": [], "start": 1753140072.245855, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/7b4681f3a79c415079e436b7e5e9f3ac.blade.php__components::7b4681f3a79c415079e436b7e5e9f3ac", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F7b4681f3a79c415079e436b7e5e9f3ac.blade.php:1", "ajax": false, "filename": "7b4681f3a79c415079e436b7e5e9f3ac.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::7b4681f3a79c415079e436b7e5e9f3ac"}, {"name": "1x packages/shortcode::partials.shortcode-button", "param_count": null, "params": [], "start": 1753140072.268582, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/packages/shortcode/resources/views/partials/shortcode-button.blade.phppackages/shortcode::partials.shortcode-button", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fshortcode%2Fresources%2Fviews%2Fpartials%2Fshortcode-button.blade.php:1", "ajax": false, "filename": "shortcode-button.blade.php", "line": "?"}, "render_count": 1, "name_original": "packages/shortcode::partials.shortcode-button"}, {"name": "1x __components::5d3f310865558ef9371c1fb4b468d742", "param_count": null, "params": [], "start": 1753140072.490598, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/5d3f310865558ef9371c1fb4b468d742.blade.php__components::5d3f310865558ef9371c1fb4b468d742", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F5d3f310865558ef9371c1fb4b468d742.blade.php:1", "ajax": false, "filename": "5d3f310865558ef9371c1fb4b468d742.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5d3f310865558ef9371c1fb4b468d742"}, {"name": "1x packages/shortcode::partials.shortcode-modal", "param_count": null, "params": [], "start": 1753140072.508475, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/packages/shortcode/resources/views/partials/shortcode-modal.blade.phppackages/shortcode::partials.shortcode-modal", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fshortcode%2Fresources%2Fviews%2Fpartials%2Fshortcode-modal.blade.php:1", "ajax": false, "filename": "shortcode-modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "packages/shortcode::partials.shortcode-modal"}, {"name": "2x __components::3cec1c87224222bda738c53f782c5bc1", "param_count": null, "params": [], "start": 1753140074.177942, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/3cec1c87224222bda738c53f782c5bc1.blade.php__components::3cec1c87224222bda738c53f782c5bc1", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F3cec1c87224222bda738c53f782c5bc1.blade.php:1", "ajax": false, "filename": "3cec1c87224222bda738c53f782c5bc1.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::3cec1c87224222bda738c53f782c5bc1"}, {"name": "1x __components::123a2f76ca745c01f392c79275e8e77b", "param_count": null, "params": [], "start": 1753140074.178708, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/123a2f76ca745c01f392c79275e8e77b.blade.php__components::123a2f76ca745c01f392c79275e8e77b", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F123a2f76ca745c01f392c79275e8e77b.blade.php:1", "ajax": false, "filename": "123a2f76ca745c01f392c79275e8e77b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::123a2f76ca745c01f392c79275e8e77b"}, {"name": "35x ********************************::card.title", "param_count": null, "params": [], "start": 1753140074.182309, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/card/title.blade.php********************************::card.title", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Ftitle.blade.php:1", "ajax": false, "filename": "title.blade.php", "line": "?"}, "render_count": 35, "name_original": "********************************::card.title"}, {"name": "27x ********************************::card.subtitle", "param_count": null, "params": [], "start": 1753140074.184534, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/card/subtitle.blade.php********************************::card.subtitle", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Fsubtitle.blade.php:1", "ajax": false, "filename": "subtitle.blade.php", "line": "?"}, "render_count": 27, "name_original": "********************************::card.subtitle"}, {"name": "35x ********************************::card.header.index", "param_count": null, "params": [], "start": 1753140074.214546, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/card/header/index.blade.php********************************::card.header.index", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Fheader%2Findex.blade.php:1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 35, "name_original": "********************************::card.header.index"}, {"name": "36x ********************************::card.index", "param_count": null, "params": [], "start": 1753140074.215494, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/card/index.blade.php********************************::card.index", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Findex.blade.php:1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 36, "name_original": "********************************::card.index"}, {"name": "1x ********************************::empty-state", "param_count": null, "params": [], "start": 1753140074.272953, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/empty-state.blade.php********************************::empty-state", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fempty-state.blade.php:1", "ajax": false, "filename": "empty-state.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::empty-state"}, {"name": "1x __components::63cd1411f8b671aa6ea66cfd91c420b5", "param_count": null, "params": [], "start": 1753140074.478988, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/63cd1411f8b671aa6ea66cfd91c420b5.blade.php__components::63cd1411f8b671aa6ea66cfd91c420b5", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F63cd1411f8b671aa6ea66cfd91c420b5.blade.php:1", "ajax": false, "filename": "63cd1411f8b671aa6ea66cfd91c420b5.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::63cd1411f8b671aa6ea66cfd91c420b5"}, {"name": "6x ********************************::modal", "param_count": null, "params": [], "start": 1753140074.503952, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/modal.blade.php********************************::modal", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal.blade.php:1", "ajax": false, "filename": "modal.blade.php", "line": "?"}, "render_count": 6, "name_original": "********************************::modal"}, {"name": "6x ********************************::modal.close-button", "param_count": null, "params": [], "start": 1753140074.505218, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/modal/close-button.blade.php********************************::modal.close-button", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal%2Fclose-button.blade.php:1", "ajax": false, "filename": "close-button.blade.php", "line": "?"}, "render_count": 6, "name_original": "********************************::modal.close-button"}, {"name": "1x core/base::forms.partials.ckeditor", "param_count": null, "params": [], "start": 1753140074.510205, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/ckeditor.blade.phpcore/base::forms.partials.ckeditor", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fckeditor.blade.php:1", "ajax": false, "filename": "ckeditor.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.partials.ckeditor"}, {"name": "1x core/base::forms.fields.media-images", "param_count": null, "params": [], "start": 1753140074.553928, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/media-images.blade.phpcore/base::forms.fields.media-images", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fmedia-images.blade.php:1", "ajax": false, "filename": "media-images.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.media-images"}, {"name": "1x core/base::forms.partials.images", "param_count": null, "params": [], "start": 1753140074.695435, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/images.blade.phpcore/base::forms.partials.images", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fimages.blade.php:1", "ajax": false, "filename": "images.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.partials.images"}, {"name": "1x ********************************::form.images", "param_count": null, "params": [], "start": 1753140074.818406, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/images.blade.php********************************::form.images", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fimages.blade.php:1", "ajax": false, "filename": "images.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::form.images"}, {"name": "1x __components::6e68593c2024c25e21ccc0ea08c838db", "param_count": null, "params": [], "start": 1753140075.279474, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/6e68593c2024c25e21ccc0ea08c838db.blade.php__components::6e68593c2024c25e21ccc0ea08c838db", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F6e68593c2024c25e21ccc0ea08c838db.blade.php:1", "ajax": false, "filename": "6e68593c2024c25e21ccc0ea08c838db.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::6e68593c2024c25e21ccc0ea08c838db"}, {"name": "13x ********************************::image", "param_count": null, "params": [], "start": 1753140075.419841, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/image.blade.php********************************::image", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fimage.blade.php:1", "ajax": false, "filename": "image.blade.php", "line": "?"}, "render_count": 13, "name_original": "********************************::image"}, {"name": "13x __components::1c3e3c2b5f3306f2c083e21b9339fe63", "param_count": null, "params": [], "start": 1753140075.478646, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/1c3e3c2b5f3306f2c083e21b9339fe63.blade.php__components::1c3e3c2b5f3306f2c083e21b9339fe63", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F1c3e3c2b5f3306f2c083e21b9339fe63.blade.php:1", "ajax": false, "filename": "1c3e3c2b5f3306f2c083e21b9339fe63.blade.php", "line": "?"}, "render_count": 13, "name_original": "__components::1c3e3c2b5f3306f2c083e21b9339fe63"}, {"name": "5x core/base::forms.fields.custom-select", "param_count": null, "params": [], "start": 1753140075.61776, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/custom-select.blade.phpcore/base::forms.fields.custom-select", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fcustom-select.blade.php:1", "ajax": false, "filename": "custom-select.blade.php", "line": "?"}, "render_count": 5, "name_original": "core/base::forms.fields.custom-select"}, {"name": "6x core/base::forms.partials.custom-select", "param_count": null, "params": [], "start": 1753140075.770735, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/custom-select.blade.phpcore/base::forms.partials.custom-select", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fcustom-select.blade.php:1", "ajax": false, "filename": "custom-select.blade.php", "line": "?"}, "render_count": 6, "name_original": "core/base::forms.partials.custom-select"}, {"name": "1x theme.xmetr::partials.fields.google-map-autocomplete-field", "param_count": null, "params": [], "start": 1753140075.860822, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/partials/fields/google-map-autocomplete-field.blade.phptheme.xmetr::partials.fields.google-map-autocomplete-field", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fpartials%2Ffields%2Fgoogle-map-autocomplete-field.blade.php:1", "ajax": false, "filename": "google-map-autocomplete-field.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::partials.fields.google-map-autocomplete-field"}, {"name": "8x core/base::forms.fields.html", "param_count": null, "params": [], "start": 1753140076.040318, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/html.blade.phpcore/base::forms.fields.html", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fhtml.blade.php:1", "ajax": false, "filename": "html.blade.php", "line": "?"}, "render_count": 8, "name_original": "core/base::forms.fields.html"}, {"name": "7x laravel-form-builder::text", "param_count": null, "params": [], "start": 1753140076.166305, "type": "php", "hash": "phpD:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src/../resources/views/text.phplaravel-form-builder::text", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fform-builder%2Fresources%2Fviews%2Ftext.php:1", "ajax": false, "filename": "text.php", "line": "?"}, "render_count": 7, "name_original": "laravel-form-builder::text"}, {"name": "6x ********************************::card.body.index", "param_count": null, "params": [], "start": 1753140076.191803, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/card/body/index.blade.php********************************::card.body.index", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Fbody%2Findex.blade.php:1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 6, "name_original": "********************************::card.body.index"}, {"name": "2x core/base::forms.partials.meta-box", "param_count": null, "params": [], "start": 1753140076.192661, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/meta-box.blade.phpcore/base::forms.partials.meta-box", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fmeta-box.blade.php:1", "ajax": false, "filename": "meta-box.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::forms.partials.meta-box"}, {"name": "1x plugins/real-estate::partials.form-facilities", "param_count": null, "params": [], "start": **********.489257, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/real-estate/resources/views/partials/form-facilities.blade.phpplugins/real-estate::partials.form-facilities", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fresources%2Fviews%2Fpartials%2Fform-facilities.blade.php:1", "ajax": false, "filename": "form-facilities.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/real-estate::partials.form-facilities"}, {"name": "1x __components::e38e2f7375f7e06238efe5faa11bb3f5", "param_count": null, "params": [], "start": **********.680016, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/e38e2f7375f7e06238efe5faa11bb3f5.blade.php__components::e38e2f7375f7e06238efe5faa11bb3f5", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fe38e2f7375f7e06238efe5faa11bb3f5.blade.php:1", "ajax": false, "filename": "e38e2f7375f7e06238efe5faa11bb3f5.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e38e2f7375f7e06238efe5faa11bb3f5"}, {"name": "1x core/base::forms.form-content-only", "param_count": null, "params": [], "start": **********.692816, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/form-content-only.blade.phpcore/base::forms.form-content-only", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fform-content-only.blade.php:1", "ajax": false, "filename": "form-content-only.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.form-content-only"}, {"name": "1x core/base::forms.fields.media-image", "param_count": null, "params": [], "start": **********.772403, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/media-image.blade.phpcore/base::forms.fields.media-image", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fmedia-image.blade.php:1", "ajax": false, "filename": "media-image.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.media-image"}, {"name": "1x core/base::forms.partials.image", "param_count": null, "params": [], "start": **********.934762, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/image.blade.phpcore/base::forms.partials.image", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fimage.blade.php:1", "ajax": false, "filename": "image.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.partials.image"}, {"name": "1x ********************************::form.image", "param_count": null, "params": [], "start": 1753140078.060858, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/image.blade.php********************************::form.image", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fimage.blade.php:1", "ajax": false, "filename": "image.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::form.image"}, {"name": "2x core/base::forms.fields.custom-radio", "param_count": null, "params": [], "start": 1753140078.461297, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/custom-radio.blade.phpcore/base::forms.fields.custom-radio", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fcustom-radio.blade.php:1", "ajax": false, "filename": "custom-radio.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::forms.fields.custom-radio"}, {"name": "2x core/base::forms.partials.custom-radio", "param_count": null, "params": [], "start": 1753140078.46351, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/custom-radio.blade.phpcore/base::forms.partials.custom-radio", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fcustom-radio.blade.php:1", "ajax": false, "filename": "custom-radio.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::forms.partials.custom-radio"}, {"name": "4x ********************************::form.radio", "param_count": null, "params": [], "start": 1753140078.601654, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/radio.blade.php********************************::form.radio", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fradio.blade.php:1", "ajax": false, "filename": "radio.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::form.radio"}, {"name": "1x packages/seo-helper::meta-box", "param_count": null, "params": [], "start": 1753140078.689463, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/packages/seo-helper/resources/views/meta-box.blade.phppackages/seo-helper::meta-box", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fseo-helper%2Fresources%2Fviews%2Fmeta-box.blade.php:1", "ajax": false, "filename": "meta-box.blade.php", "line": "?"}, "render_count": 1, "name_original": "packages/seo-helper::meta-box"}, {"name": "1x ********************************::card.actions", "param_count": null, "params": [], "start": 1753140079.136089, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/card/actions.blade.php********************************::card.actions", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Factions.blade.php:1", "ajax": false, "filename": "actions.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::card.actions"}, {"name": "1x ********************************::hr", "param_count": null, "params": [], "start": 1753140079.148281, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/hr.blade.php********************************::hr", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fhr.blade.php:1", "ajax": false, "filename": "hr.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::hr"}, {"name": "2x core/base::elements.meta-box-wrap", "param_count": null, "params": [], "start": 1753140079.230777, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/elements/meta-box-wrap.blade.phpcore/base::elements.meta-box-wrap", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Felements%2Fmeta-box-wrap.blade.php:1", "ajax": false, "filename": "meta-box-wrap.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::elements.meta-box-wrap"}, {"name": "3x core/base::elements.meta-box", "param_count": null, "params": [], "start": 1753140079.720264, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/elements/meta-box.blade.phpcore/base::elements.meta-box", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Felements%2Fmeta-box.blade.php:1", "ajax": false, "filename": "meta-box.blade.php", "line": "?"}, "render_count": 3, "name_original": "core/base::elements.meta-box"}, {"name": "1x core/base::forms.partials.form-actions", "param_count": null, "params": [], "start": 1753140079.742461, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/form-actions.blade.phpcore/base::forms.partials.form-actions", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fform-actions.blade.php:1", "ajax": false, "filename": "form-actions.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.partials.form-actions"}, {"name": "2x core/base::forms.partials.form-buttons", "param_count": null, "params": [], "start": **********.278884, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/form-buttons.blade.phpcore/base::forms.partials.form-buttons", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fform-buttons.blade.php:1", "ajax": false, "filename": "form-buttons.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::forms.partials.form-buttons"}, {"name": "2x __components::d25e365b684c7fa9ad5ec13cfb768fc1", "param_count": null, "params": [], "start": **********.602424, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/d25e365b684c7fa9ad5ec13cfb768fc1.blade.php__components::d25e365b684c7fa9ad5ec13cfb768fc1", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fd25e365b684c7fa9ad5ec13cfb768fc1.blade.php:1", "ajax": false, "filename": "d25e365b684c7fa9ad5ec13cfb768fc1.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::d25e365b684c7fa9ad5ec13cfb768fc1"}, {"name": "2x __components::5db12cde11beb11dd9ef0499874ec197", "param_count": null, "params": [], "start": **********.635662, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/5db12cde11beb11dd9ef0499874ec197.blade.php__components::5db12cde11beb11dd9ef0499874ec197", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F5db12cde11beb11dd9ef0499874ec197.blade.php:1", "ajax": false, "filename": "5db12cde11beb11dd9ef0499874ec197.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::5db12cde11beb11dd9ef0499874ec197"}, {"name": "1x core/base::layouts.partials.breadcrumbs", "param_count": null, "params": [], "start": **********.662923, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/partials/breadcrumbs.blade.phpcore/base::layouts.partials.breadcrumbs", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fbreadcrumbs.blade.php:1", "ajax": false, "filename": "breadcrumbs.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.breadcrumbs"}, {"name": "1x plugins/language-advanced::language-box", "param_count": null, "params": [], "start": **********.731615, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/language-advanced/resources/views/language-box.blade.phpplugins/language-advanced::language-box", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage-advanced%2Fresources%2Fviews%2Flanguage-box.blade.php:1", "ajax": false, "filename": "language-box.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/language-advanced::language-box"}, {"name": "4x __components::********************************", "param_count": null, "params": [], "start": **********.938437, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/********************************.blade.php__components::********************************", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F********************************.blade.php:1", "ajax": false, "filename": "********************************.blade.php", "line": "?"}, "render_count": 4, "name_original": "__components::********************************"}, {"name": "1x core/base::forms.fields.autocomplete", "param_count": null, "params": [], "start": **********.999082, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/autocomplete.blade.phpcore/base::forms.fields.autocomplete", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fautocomplete.blade.php:1", "ajax": false, "filename": "autocomplete.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.autocomplete"}, {"name": "1x core/base::forms.partials.autocomplete", "param_count": null, "params": [], "start": **********.192225, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/autocomplete.blade.phpcore/base::forms.partials.autocomplete", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fautocomplete.blade.php:1", "ajax": false, "filename": "autocomplete.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.partials.autocomplete"}, {"name": "1x core/js-validation::bootstrap", "param_count": null, "params": [], "start": **********.301734, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/js-validation/resources/views/bootstrap.blade.phpcore/js-validation::bootstrap", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fjs-validation%2Fresources%2Fviews%2Fbootstrap.blade.php:1", "ajax": false, "filename": "bootstrap.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/js-validation::bootstrap"}, {"name": "1x core/base::layouts.master", "param_count": null, "params": [], "start": **********.303466, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/master.blade.phpcore/base::layouts.master", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fmaster.blade.php:1", "ajax": false, "filename": "master.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.master"}, {"name": "1x core/base::layouts.vertical.partials.before-content", "param_count": null, "params": [], "start": **********.304593, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/vertical/partials/before-content.blade.phpcore/base::layouts.vertical.partials.before-content", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fbefore-content.blade.php:1", "ajax": false, "filename": "before-content.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.before-content"}, {"name": "1x core/base::layouts.vertical.partials.header", "param_count": null, "params": [], "start": **********.305025, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/vertical/partials/header.blade.phpcore/base::layouts.vertical.partials.header", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fheader.blade.php:1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.header"}, {"name": "2x __components::133aa97c11fca0f84f02ebcb9fd067dc", "param_count": null, "params": [], "start": **********.306913, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/133aa97c11fca0f84f02ebcb9fd067dc.blade.php__components::133aa97c11fca0f84f02ebcb9fd067dc", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F133aa97c11fca0f84f02ebcb9fd067dc.blade.php:1", "ajax": false, "filename": "133aa97c11fca0f84f02ebcb9fd067dc.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::133aa97c11fca0f84f02ebcb9fd067dc"}, {"name": "2x core/base::partials.logo", "param_count": null, "params": [], "start": **********.307301, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/partials/logo.blade.phpcore/base::partials.logo", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fpartials%2Flogo.blade.php:1", "ajax": false, "filename": "logo.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::partials.logo"}, {"name": "1x core/base::global-search.navbar-input", "param_count": null, "params": [], "start": **********.307906, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/global-search/navbar-input.blade.phpcore/base::global-search.navbar-input", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fglobal-search%2Fnavbar-input.blade.php:1", "ajax": false, "filename": "navbar-input.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::global-search.navbar-input"}, {"name": "1x __components::dc988bb05638c97d86c3bc8a9b727e31", "param_count": null, "params": [], "start": **********.31357, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/dc988bb05638c97d86c3bc8a9b727e31.blade.php__components::dc988bb05638c97d86c3bc8a9b727e31", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fdc988bb05638c97d86c3bc8a9b727e31.blade.php:1", "ajax": false, "filename": "dc988bb05638c97d86c3bc8a9b727e31.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::dc988bb05638c97d86c3bc8a9b727e31"}, {"name": "1x core/base::layouts.partials.theme-toggle", "param_count": null, "params": [], "start": **********.313933, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/partials/theme-toggle.blade.phpcore/base::layouts.partials.theme-toggle", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Ftheme-toggle.blade.php:1", "ajax": false, "filename": "theme-toggle.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.theme-toggle"}, {"name": "1x __components::ec711b4f47c4b42ebc81a7564c1d8d33", "param_count": null, "params": [], "start": **********.315883, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/ec711b4f47c4b42ebc81a7564c1d8d33.blade.php__components::ec711b4f47c4b42ebc81a7564c1d8d33", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fec711b4f47c4b42ebc81a7564c1d8d33.blade.php:1", "ajax": false, "filename": "ec711b4f47c4b42ebc81a7564c1d8d33.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ec711b4f47c4b42ebc81a7564c1d8d33"}, {"name": "1x core/base::notification.nav-item", "param_count": null, "params": [], "start": **********.31747, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/notification/nav-item.blade.phpcore/base::notification.nav-item", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fnotification%2Fnav-item.blade.php:1", "ajax": false, "filename": "nav-item.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::notification.nav-item"}, {"name": "1x __components::306e03d3dc5634ee2e82192f553c6f9b", "param_count": null, "params": [], "start": **********.318822, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/306e03d3dc5634ee2e82192f553c6f9b.blade.php__components::306e03d3dc5634ee2e82192f553c6f9b", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F306e03d3dc5634ee2e82192f553c6f9b.blade.php:1", "ajax": false, "filename": "306e03d3dc5634ee2e82192f553c6f9b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::306e03d3dc5634ee2e82192f553c6f9b"}, {"name": "1x core/base::layouts.partials.user-menu", "param_count": null, "params": [], "start": **********.324421, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/partials/user-menu.blade.phpcore/base::layouts.partials.user-menu", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fuser-menu.blade.php:1", "ajax": false, "filename": "user-menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.user-menu"}, {"name": "4x ********************************::dropdown.item", "param_count": null, "params": [], "start": **********.329846, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/dropdown/item.blade.php********************************::dropdown.item", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdropdown%2Fitem.blade.php:1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::dropdown.item"}, {"name": "2x __components::2907df26a6102c24ab0c37391217b338", "param_count": null, "params": [], "start": **********.331289, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/2907df26a6102c24ab0c37391217b338.blade.php__components::2907df26a6102c24ab0c37391217b338", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F2907df26a6102c24ab0c37391217b338.blade.php:1", "ajax": false, "filename": "2907df26a6102c24ab0c37391217b338.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::2907df26a6102c24ab0c37391217b338"}, {"name": "2x __components::f38bffca7b8a1a50e97a6950ffd66c5c", "param_count": null, "params": [], "start": **********.332979, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/f38bffca7b8a1a50e97a6950ffd66c5c.blade.php__components::f38bffca7b8a1a50e97a6950ffd66c5c", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Ff38bffca7b8a1a50e97a6950ffd66c5c.blade.php:1", "ajax": false, "filename": "f38bffca7b8a1a50e97a6950ffd66c5c.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::f38bffca7b8a1a50e97a6950ffd66c5c"}, {"name": "2x ********************************::dropdown.index", "param_count": null, "params": [], "start": **********.333258, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/dropdown/index.blade.php********************************::dropdown.index", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdropdown%2Findex.blade.php:1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::dropdown.index"}, {"name": "1x core/base::layouts.vertical.partials.aside", "param_count": null, "params": [], "start": **********.333967, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/vertical/partials/aside.blade.phpcore/base::layouts.vertical.partials.aside", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Faside.blade.php:1", "ajax": false, "filename": "aside.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.aside"}, {"name": "1x core/base::layouts.vertical.partials.sidebar", "param_count": null, "params": [], "start": **********.341556, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/vertical/partials/sidebar.blade.phpcore/base::layouts.vertical.partials.sidebar", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fsidebar.blade.php:1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.sidebar"}, {"name": "1x core/base::layouts.partials.navbar-nav", "param_count": null, "params": [], "start": **********.342063, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/partials/navbar-nav.blade.phpcore/base::layouts.partials.navbar-nav", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fnavbar-nav.blade.php:1", "ajax": false, "filename": "navbar-nav.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.navbar-nav"}, {"name": "17x core/base::layouts.partials.navbar-nav-item", "param_count": null, "params": [], "start": **********.364682, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/partials/navbar-nav-item.blade.phpcore/base::layouts.partials.navbar-nav-item", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fnavbar-nav-item.blade.php:1", "ajax": false, "filename": "navbar-nav-item.blade.php", "line": "?"}, "render_count": 17, "name_original": "core/base::layouts.partials.navbar-nav-item"}, {"name": "48x core/base::layouts.partials.navbar-nav-item-link", "param_count": null, "params": [], "start": **********.366302, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/partials/navbar-nav-item-link.blade.phpcore/base::layouts.partials.navbar-nav-item-link", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fnavbar-nav-item-link.blade.php:1", "ajax": false, "filename": "navbar-nav-item-link.blade.php", "line": "?"}, "render_count": 48, "name_original": "core/base::layouts.partials.navbar-nav-item-link"}, {"name": "1x __components::af68cda57c5ca67f3b8a7729953880bc", "param_count": null, "params": [], "start": **********.369302, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/af68cda57c5ca67f3b8a7729953880bc.blade.php__components::af68cda57c5ca67f3b8a7729953880bc", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Faf68cda57c5ca67f3b8a7729953880bc.blade.php:1", "ajax": false, "filename": "af68cda57c5ca67f3b8a7729953880bc.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::af68cda57c5ca67f3b8a7729953880bc"}, {"name": "1x __components::5def649a5a47936dda7e47db6ffcfa75", "param_count": null, "params": [], "start": **********.374132, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/5def649a5a47936dda7e47db6ffcfa75.blade.php__components::5def649a5a47936dda7e47db6ffcfa75", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F5def649a5a47936dda7e47db6ffcfa75.blade.php:1", "ajax": false, "filename": "5def649a5a47936dda7e47db6ffcfa75.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5def649a5a47936dda7e47db6ffcfa75"}, {"name": "14x __components::d890ecc3acbc4ef41a8ece9e81698457", "param_count": null, "params": [], "start": **********.376021, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/d890ecc3acbc4ef41a8ece9e81698457.blade.php__components::d890ecc3acbc4ef41a8ece9e81698457", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fd890ecc3acbc4ef41a8ece9e81698457.blade.php:1", "ajax": false, "filename": "d890ecc3acbc4ef41a8ece9e81698457.blade.php", "line": "?"}, "render_count": 14, "name_original": "__components::d890ecc3acbc4ef41a8ece9e81698457"}, {"name": "1x __components::dd4c2087b0a47210b5b4e3ee87ef3eca", "param_count": null, "params": [], "start": **********.389782, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/dd4c2087b0a47210b5b4e3ee87ef3eca.blade.php__components::dd4c2087b0a47210b5b4e3ee87ef3eca", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fdd4c2087b0a47210b5b4e3ee87ef3eca.blade.php:1", "ajax": false, "filename": "dd4c2087b0a47210b5b4e3ee87ef3eca.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::dd4c2087b0a47210b5b4e3ee87ef3eca"}, {"name": "1x __components::ca20cd1247722214b06db9aa7c493b27", "param_count": null, "params": [], "start": **********.39494, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/ca20cd1247722214b06db9aa7c493b27.blade.php__components::ca20cd1247722214b06db9aa7c493b27", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fca20cd1247722214b06db9aa7c493b27.blade.php:1", "ajax": false, "filename": "ca20cd1247722214b06db9aa7c493b27.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ca20cd1247722214b06db9aa7c493b27"}, {"name": "3x core/base::partials.navbar.badge-count", "param_count": null, "params": [], "start": **********.3959, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/partials/navbar/badge-count.blade.phpcore/base::partials.navbar.badge-count", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fpartials%2Fnavbar%2Fbadge-count.blade.php:1", "ajax": false, "filename": "badge-count.blade.php", "line": "?"}, "render_count": 3, "name_original": "core/base::partials.navbar.badge-count"}, {"name": "3x ********************************::navbar.badge-count", "param_count": null, "params": [], "start": **********.396537, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/navbar/badge-count.blade.php********************************::navbar.badge-count", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fnavbar%2Fbadge-count.blade.php:1", "ajax": false, "filename": "badge-count.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::navbar.badge-count"}, {"name": "1x __components::a3acd3bd206d0793e18d491720de886c", "param_count": null, "params": [], "start": **********.407589, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/a3acd3bd206d0793e18d491720de886c.blade.php__components::a3acd3bd206d0793e18d491720de886c", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fa3acd3bd206d0793e18d491720de886c.blade.php:1", "ajax": false, "filename": "a3acd3bd206d0793e18d491720de886c.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::a3acd3bd206d0793e18d491720de886c"}, {"name": "1x __components::fd978891e1ac33723cbffddc6658659a", "param_count": null, "params": [], "start": **********.410861, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/fd978891e1ac33723cbffddc6658659a.blade.php__components::fd978891e1ac33723cbffddc6658659a", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Ffd978891e1ac33723cbffddc6658659a.blade.php:1", "ajax": false, "filename": "fd978891e1ac33723cbffddc6658659a.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::fd978891e1ac33723cbffddc6658659a"}, {"name": "1x __components::13365b7e5a448d13150fdb4b3884b510", "param_count": null, "params": [], "start": **********.418192, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/13365b7e5a448d13150fdb4b3884b510.blade.php__components::13365b7e5a448d13150fdb4b3884b510", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F13365b7e5a448d13150fdb4b3884b510.blade.php:1", "ajax": false, "filename": "13365b7e5a448d13150fdb4b3884b510.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::13365b7e5a448d13150fdb4b3884b510"}, {"name": "2x __components::98e88d58787b8dfeb6f0d1dc0a785cfd", "param_count": null, "params": [], "start": **********.423548, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/98e88d58787b8dfeb6f0d1dc0a785cfd.blade.php__components::98e88d58787b8dfeb6f0d1dc0a785cfd", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F98e88d58787b8dfeb6f0d1dc0a785cfd.blade.php:1", "ajax": false, "filename": "98e88d58787b8dfeb6f0d1dc0a785cfd.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::98e88d58787b8dfeb6f0d1dc0a785cfd"}, {"name": "1x __components::cbce7a4c13e13a70eabb7759894a60fd", "param_count": null, "params": [], "start": **********.426192, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/cbce7a4c13e13a70eabb7759894a60fd.blade.php__components::cbce7a4c13e13a70eabb7759894a60fd", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fcbce7a4c13e13a70eabb7759894a60fd.blade.php:1", "ajax": false, "filename": "cbce7a4c13e13a70eabb7759894a60fd.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::cbce7a4c13e13a70eabb7759894a60fd"}, {"name": "1x __components::fe6fcb7551f99a7d9d1c5a4c0011f471", "param_count": null, "params": [], "start": **********.428233, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/fe6fcb7551f99a7d9d1c5a4c0011f471.blade.php__components::fe6fcb7551f99a7d9d1c5a4c0011f471", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Ffe6fcb7551f99a7d9d1c5a4c0011f471.blade.php:1", "ajax": false, "filename": "fe6fcb7551f99a7d9d1c5a4c0011f471.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::fe6fcb7551f99a7d9d1c5a4c0011f471"}, {"name": "1x __components::351bdfbe842eff22e08f1df9d5f5beb1", "param_count": null, "params": [], "start": **********.430965, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/351bdfbe842eff22e08f1df9d5f5beb1.blade.php__components::351bdfbe842eff22e08f1df9d5f5beb1", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F351bdfbe842eff22e08f1df9d5f5beb1.blade.php:1", "ajax": false, "filename": "351bdfbe842eff22e08f1df9d5f5beb1.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::351bdfbe842eff22e08f1df9d5f5beb1"}, {"name": "1x __components::0343a1b0800146d7d9cf6a9514ec7bf4", "param_count": null, "params": [], "start": **********.441546, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/0343a1b0800146d7d9cf6a9514ec7bf4.blade.php__components::0343a1b0800146d7d9cf6a9514ec7bf4", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F0343a1b0800146d7d9cf6a9514ec7bf4.blade.php:1", "ajax": false, "filename": "0343a1b0800146d7d9cf6a9514ec7bf4.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0343a1b0800146d7d9cf6a9514ec7bf4"}, {"name": "1x __components::15422be7d0f2aaf8c8244c1e9db20ad9", "param_count": null, "params": [], "start": **********.445244, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/15422be7d0f2aaf8c8244c1e9db20ad9.blade.php__components::15422be7d0f2aaf8c8244c1e9db20ad9", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F15422be7d0f2aaf8c8244c1e9db20ad9.blade.php:1", "ajax": false, "filename": "15422be7d0f2aaf8c8244c1e9db20ad9.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::15422be7d0f2aaf8c8244c1e9db20ad9"}, {"name": "1x __components::2e5e965add6d0ee3aadb02ca38e70825", "param_count": null, "params": [], "start": **********.44844, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/2e5e965add6d0ee3aadb02ca38e70825.blade.php__components::2e5e965add6d0ee3aadb02ca38e70825", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F2e5e965add6d0ee3aadb02ca38e70825.blade.php:1", "ajax": false, "filename": "2e5e965add6d0ee3aadb02ca38e70825.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::2e5e965add6d0ee3aadb02ca38e70825"}, {"name": "1x __components::5b8d99843e0f8eff6046d7236026b187", "param_count": null, "params": [], "start": **********.450198, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/5b8d99843e0f8eff6046d7236026b187.blade.php__components::5b8d99843e0f8eff6046d7236026b187", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F5b8d99843e0f8eff6046d7236026b187.blade.php:1", "ajax": false, "filename": "5b8d99843e0f8eff6046d7236026b187.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5b8d99843e0f8eff6046d7236026b187"}, {"name": "1x __components::dd248739db8ba923ebfe1f426bb71a38", "param_count": null, "params": [], "start": **********.451884, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/dd248739db8ba923ebfe1f426bb71a38.blade.php__components::dd248739db8ba923ebfe1f426bb71a38", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fdd248739db8ba923ebfe1f426bb71a38.blade.php:1", "ajax": false, "filename": "dd248739db8ba923ebfe1f426bb71a38.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::dd248739db8ba923ebfe1f426bb71a38"}, {"name": "2x __components::1d6f928aaf1e585d3246cb3bee8fdd45", "param_count": null, "params": [], "start": **********.454595, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/1d6f928aaf1e585d3246cb3bee8fdd45.blade.php__components::1d6f928aaf1e585d3246cb3bee8fdd45", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F1d6f928aaf1e585d3246cb3bee8fdd45.blade.php:1", "ajax": false, "filename": "1d6f928aaf1e585d3246cb3bee8fdd45.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::1d6f928aaf1e585d3246cb3bee8fdd45"}, {"name": "1x __components::acbc3e3e19c70a7ebb0e7a5d98d84fbe", "param_count": null, "params": [], "start": **********.458382, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/acbc3e3e19c70a7ebb0e7a5d98d84fbe.blade.php__components::acbc3e3e19c70a7ebb0e7a5d98d84fbe", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Facbc3e3e19c70a7ebb0e7a5d98d84fbe.blade.php:1", "ajax": false, "filename": "acbc3e3e19c70a7ebb0e7a5d98d84fbe.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::acbc3e3e19c70a7ebb0e7a5d98d84fbe"}, {"name": "1x __components::6e0b6ed9bf49c6ad9d02af2c7f911103", "param_count": null, "params": [], "start": **********.462867, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/6e0b6ed9bf49c6ad9d02af2c7f911103.blade.php__components::6e0b6ed9bf49c6ad9d02af2c7f911103", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F6e0b6ed9bf49c6ad9d02af2c7f911103.blade.php:1", "ajax": false, "filename": "6e0b6ed9bf49c6ad9d02af2c7f911103.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::6e0b6ed9bf49c6ad9d02af2c7f911103"}, {"name": "1x __components::dac323985d9d2618ad252313442aaf03", "param_count": null, "params": [], "start": **********.468173, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/dac323985d9d2618ad252313442aaf03.blade.php__components::dac323985d9d2618ad252313442aaf03", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fdac323985d9d2618ad252313442aaf03.blade.php:1", "ajax": false, "filename": "dac323985d9d2618ad252313442aaf03.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::dac323985d9d2618ad252313442aaf03"}, {"name": "1x __components::298cd8a12b86a6f371ff06491a0822fa", "param_count": null, "params": [], "start": **********.471972, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/298cd8a12b86a6f371ff06491a0822fa.blade.php__components::298cd8a12b86a6f371ff06491a0822fa", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F298cd8a12b86a6f371ff06491a0822fa.blade.php:1", "ajax": false, "filename": "298cd8a12b86a6f371ff06491a0822fa.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::298cd8a12b86a6f371ff06491a0822fa"}, {"name": "1x __components::c59870a61b233f0766e3260625bdb025", "param_count": null, "params": [], "start": **********.47556, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/c59870a61b233f0766e3260625bdb025.blade.php__components::c59870a61b233f0766e3260625bdb025", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fc59870a61b233f0766e3260625bdb025.blade.php:1", "ajax": false, "filename": "c59870a61b233f0766e3260625bdb025.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::c59870a61b233f0766e3260625bdb025"}, {"name": "1x __components::3890eca5d46a147ef99ddf994453d2ec", "param_count": null, "params": [], "start": **********.478847, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/3890eca5d46a147ef99ddf994453d2ec.blade.php__components::3890eca5d46a147ef99ddf994453d2ec", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F3890eca5d46a147ef99ddf994453d2ec.blade.php:1", "ajax": false, "filename": "3890eca5d46a147ef99ddf994453d2ec.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::3890eca5d46a147ef99ddf994453d2ec"}, {"name": "1x __components::ff1fde71531b073b2c658173537aaea5", "param_count": null, "params": [], "start": **********.482161, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/ff1fde71531b073b2c658173537aaea5.blade.php__components::ff1fde71531b073b2c658173537aaea5", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fff1fde71531b073b2c658173537aaea5.blade.php:1", "ajax": false, "filename": "ff1fde71531b073b2c658173537aaea5.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ff1fde71531b073b2c658173537aaea5"}, {"name": "1x __components::5cef0de51e1489c31c7fcb5d7f2f6a97", "param_count": null, "params": [], "start": **********.485223, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/5cef0de51e1489c31c7fcb5d7f2f6a97.blade.php__components::5cef0de51e1489c31c7fcb5d7f2f6a97", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F5cef0de51e1489c31c7fcb5d7f2f6a97.blade.php:1", "ajax": false, "filename": "5cef0de51e1489c31c7fcb5d7f2f6a97.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5cef0de51e1489c31c7fcb5d7f2f6a97"}, {"name": "1x __components::89cb89d3fdb0a0a12f8aa61073c231e6", "param_count": null, "params": [], "start": **********.488201, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/89cb89d3fdb0a0a12f8aa61073c231e6.blade.php__components::89cb89d3fdb0a0a12f8aa61073c231e6", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F89cb89d3fdb0a0a12f8aa61073c231e6.blade.php:1", "ajax": false, "filename": "89cb89d3fdb0a0a12f8aa61073c231e6.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::89cb89d3fdb0a0a12f8aa61073c231e6"}, {"name": "1x __components::5a5b09d3f2ee0ddb2b536feb532d230a", "param_count": null, "params": [], "start": **********.490229, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/5a5b09d3f2ee0ddb2b536feb532d230a.blade.php__components::5a5b09d3f2ee0ddb2b536feb532d230a", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F5a5b09d3f2ee0ddb2b536feb532d230a.blade.php:1", "ajax": false, "filename": "5a5b09d3f2ee0ddb2b536feb532d230a.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5a5b09d3f2ee0ddb2b536feb532d230a"}, {"name": "1x __components::fedc652debeb23dcbb31a98830baa397", "param_count": null, "params": [], "start": **********.491965, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/fedc652debeb23dcbb31a98830baa397.blade.php__components::fedc652debeb23dcbb31a98830baa397", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Ffedc652debeb23dcbb31a98830baa397.blade.php:1", "ajax": false, "filename": "fedc652debeb23dcbb31a98830baa397.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::fedc652debeb23dcbb31a98830baa397"}, {"name": "1x __components::ff3b2cf4e42e74e63db76ff05c5f2374", "param_count": null, "params": [], "start": **********.495431, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/ff3b2cf4e42e74e63db76ff05c5f2374.blade.php__components::ff3b2cf4e42e74e63db76ff05c5f2374", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fff3b2cf4e42e74e63db76ff05c5f2374.blade.php:1", "ajax": false, "filename": "ff3b2cf4e42e74e63db76ff05c5f2374.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ff3b2cf4e42e74e63db76ff05c5f2374"}, {"name": "1x __components::745871da7c635a3f461dfaeeef54a48e", "param_count": null, "params": [], "start": **********.498709, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/745871da7c635a3f461dfaeeef54a48e.blade.php__components::745871da7c635a3f461dfaeeef54a48e", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F745871da7c635a3f461dfaeeef54a48e.blade.php:1", "ajax": false, "filename": "745871da7c635a3f461dfaeeef54a48e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::745871da7c635a3f461dfaeeef54a48e"}, {"name": "1x __components::2b3233eda7e50501ef45fd875b12da49", "param_count": null, "params": [], "start": **********.502427, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/2b3233eda7e50501ef45fd875b12da49.blade.php__components::2b3233eda7e50501ef45fd875b12da49", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F2b3233eda7e50501ef45fd875b12da49.blade.php:1", "ajax": false, "filename": "2b3233eda7e50501ef45fd875b12da49.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::2b3233eda7e50501ef45fd875b12da49"}, {"name": "1x __components::b13663c834a4ae876ef8f72aa0610e8c", "param_count": null, "params": [], "start": **********.505524, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/b13663c834a4ae876ef8f72aa0610e8c.blade.php__components::b13663c834a4ae876ef8f72aa0610e8c", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fb13663c834a4ae876ef8f72aa0610e8c.blade.php:1", "ajax": false, "filename": "b13663c834a4ae876ef8f72aa0610e8c.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b13663c834a4ae876ef8f72aa0610e8c"}, {"name": "1x core/base::layouts.partials.page-header", "param_count": null, "params": [], "start": **********.506409, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/partials/page-header.blade.phpcore/base::layouts.partials.page-header", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fpage-header.blade.php:1", "ajax": false, "filename": "page-header.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.page-header"}, {"name": "1x core/base::breadcrumb", "param_count": null, "params": [], "start": **********.506773, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/breadcrumb.blade.phpcore/base::breadcrumb", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fbreadcrumb.blade.php:1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::breadcrumb"}, {"name": "1x core/base::layouts.partials.footer", "param_count": null, "params": [], "start": **********.507593, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/partials/footer.blade.phpcore/base::layouts.partials.footer", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Ffooter.blade.php:1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.footer"}, {"name": "1x core/base::partials.copyright", "param_count": null, "params": [], "start": **********.507928, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/partials/copyright.blade.phpcore/base::partials.copyright", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fpartials%2Fcopyright.blade.php:1", "ajax": false, "filename": "copyright.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::partials.copyright"}, {"name": "1x core/base::layouts.vertical.partials.after-content", "param_count": null, "params": [], "start": **********.508904, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/vertical/partials/after-content.blade.phpcore/base::layouts.vertical.partials.after-content", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fafter-content.blade.php:1", "ajax": false, "filename": "after-content.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.after-content"}, {"name": "1x core/base::global-search.form", "param_count": null, "params": [], "start": **********.50973, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/global-search/form.blade.phpcore/base::global-search.form", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fglobal-search%2Fform.blade.php:1", "ajax": false, "filename": "form.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::global-search.form"}, {"name": "1x ********************************::form.index", "param_count": null, "params": [], "start": **********.514566, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/index.blade.php********************************::form.index", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Findex.blade.php:1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::form.index"}, {"name": "1x __components::414e4e803eeec6389552bb46515583c5", "param_count": null, "params": [], "start": **********.51571, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/414e4e803eeec6389552bb46515583c5.blade.php__components::414e4e803eeec6389552bb46515583c5", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F414e4e803eeec6389552bb46515583c5.blade.php:1", "ajax": false, "filename": "414e4e803eeec6389552bb46515583c5.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::414e4e803eeec6389552bb46515583c5"}, {"name": "1x __components::c47a448d99d5719cb034f7947c739ff8", "param_count": null, "params": [], "start": **********.516636, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/c47a448d99d5719cb034f7947c739ff8.blade.php__components::c47a448d99d5719cb034f7947c739ff8", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fc47a448d99d5719cb034f7947c739ff8.blade.php:1", "ajax": false, "filename": "c47a448d99d5719cb034f7947c739ff8.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::c47a448d99d5719cb034f7947c739ff8"}, {"name": "1x __components::e226165e1fca7eccb10f6857d7cd235a", "param_count": null, "params": [], "start": **********.517495, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/e226165e1fca7eccb10f6857d7cd235a.blade.php__components::e226165e1fca7eccb10f6857d7cd235a", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fe226165e1fca7eccb10f6857d7cd235a.blade.php:1", "ajax": false, "filename": "e226165e1fca7eccb10f6857d7cd235a.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e226165e1fca7eccb10f6857d7cd235a"}, {"name": "1x ********************************::custom-template", "param_count": null, "params": [], "start": **********.518933, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/custom-template.blade.php********************************::custom-template", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcustom-template.blade.php:1", "ajax": false, "filename": "custom-template.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::custom-template"}, {"name": "1x core/media::partials.media", "param_count": null, "params": [], "start": **********.519304, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/media/resources/views/partials/media.blade.phpcore/media::partials.media", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fresources%2Fviews%2Fpartials%2Fmedia.blade.php:1", "ajax": false, "filename": "media.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/media::partials.media"}, {"name": "1x ********************************::loading", "param_count": null, "params": [], "start": **********.520708, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/loading.blade.php********************************::loading", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Floading.blade.php:1", "ajax": false, "filename": "loading.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::loading"}, {"name": "1x core/media::config", "param_count": null, "params": [], "start": **********.527462, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/media/resources/views/config.blade.phpcore/media::config", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fresources%2Fviews%2Fconfig.blade.php:1", "ajax": false, "filename": "config.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/media::config"}, {"name": "1x ********************************::debug-badge", "param_count": null, "params": [], "start": **********.656051, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/debug-badge.blade.php********************************::debug-badge", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdebug-badge.blade.php:1", "ajax": false, "filename": "debug-badge.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::debug-badge"}, {"name": "2x ********************************::modal.action", "param_count": null, "params": [], "start": **********.656966, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/modal/action.blade.php********************************::modal.action", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal%2Faction.blade.php:1", "ajax": false, "filename": "action.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::modal.action"}, {"name": "2x ********************************::modal.alert", "param_count": null, "params": [], "start": **********.657985, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/modal/alert.blade.php********************************::modal.alert", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal%2Falert.blade.php:1", "ajax": false, "filename": "alert.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::modal.alert"}, {"name": "1x __components::dcf17957c4aa053a618fd9c312cc29fc", "param_count": null, "params": [], "start": **********.659694, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/dcf17957c4aa053a618fd9c312cc29fc.blade.php__components::dcf17957c4aa053a618fd9c312cc29fc", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fdcf17957c4aa053a618fd9c312cc29fc.blade.php:1", "ajax": false, "filename": "dcf17957c4aa053a618fd9c312cc29fc.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::dcf17957c4aa053a618fd9c312cc29fc"}, {"name": "1x __components::fcfb9f9ba5fb460899f38b71f491e1fe", "param_count": null, "params": [], "start": **********.66371, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/fcfb9f9ba5fb460899f38b71f491e1fe.blade.php__components::fcfb9f9ba5fb460899f38b71f491e1fe", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Ffcfb9f9ba5fb460899f38b71f491e1fe.blade.php:1", "ajax": false, "filename": "fcfb9f9ba5fb460899f38b71f491e1fe.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::fcfb9f9ba5fb460899f38b71f491e1fe"}, {"name": "1x ********************************::layouts.base", "param_count": null, "params": [], "start": **********.665243, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/layouts/base.blade.php********************************::layouts.base", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fbase.blade.php:1", "ajax": false, "filename": "base.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::layouts.base"}, {"name": "1x core/base::components.layouts.header", "param_count": null, "params": [], "start": **********.666692, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/layouts/header.blade.phpcore/base::components.layouts.header", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fheader.blade.php:1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::components.layouts.header"}, {"name": "1x assets::header", "param_count": null, "params": [], "start": **********.671489, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\packages\\laravel-assets\\src\\Providers/../../resources/views/header.blade.phpassets::header", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Flaravel-assets%2Fresources%2Fviews%2Fheader.blade.php:1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "assets::header"}, {"name": "1x core/base::elements.common", "param_count": null, "params": [], "start": **********.674465, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/elements/common.blade.phpcore/base::elements.common", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Felements%2Fcommon.blade.php:1", "ajax": false, "filename": "common.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::elements.common"}, {"name": "1x assets::footer", "param_count": null, "params": [], "start": **********.676888, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\packages\\laravel-assets\\src\\Providers/../../resources/views/footer.blade.phpassets::footer", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Flaravel-assets%2Fresources%2Fviews%2Ffooter.blade.php:1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "assets::footer"}, {"name": "1x core/base::notification.notification", "param_count": null, "params": [], "start": **********.678156, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/notification/notification.blade.phpcore/base::notification.notification", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fnotification%2Fnotification.blade.php:1", "ajax": false, "filename": "notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::notification.notification"}]}, "queries": {"count": 23, "nb_statements": 23, "nb_visible_statements": 23, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02088, "accumulated_duration_str": "20.88ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.82725, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 0, "width_percent": 2.155}, {"sql": "select * from `re_projects` where `re_projects`.`id` = '25' limit 1", "type": "query", "params": [], "bindings": ["25"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 19, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/ProjectController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\ProjectController.php", "line": 85}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.834346, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 2.155, "width_percent": 10.249}, {"sql": "select `name`, `id` from `re_investors`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/ProjectForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\ProjectForm.php", "line": 53}, {"index": 15, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}, {"index": 16, "namespace": null, "name": "platform/packages/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\FormBuilder.php", "line": 44}, {"index": 17, "namespace": null, "name": "platform/core/base/src/Forms/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormBuilder.php", "line": 11}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 570}], "start": **********.8656878, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "ProjectForm.php:53", "source": {"index": 14, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/ProjectForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\ProjectForm.php", "line": 53}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FForms%2FProjectForm.php:53", "ajax": false, "filename": "ProjectForm.php", "line": "53"}, "connection": "xmetr", "explain": null, "start_percent": 12.404, "width_percent": 2.826}, {"sql": "select `title`, `id` from `re_currencies`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/ProjectForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\ProjectForm.php", "line": 55}, {"index": 15, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}, {"index": 16, "namespace": null, "name": "platform/packages/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\FormBuilder.php", "line": 44}, {"index": 17, "namespace": null, "name": "platform/core/base/src/Forms/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormBuilder.php", "line": 11}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 570}], "start": **********.868099, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "ProjectForm.php:55", "source": {"index": 14, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/ProjectForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\ProjectForm.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FForms%2FProjectForm.php:55", "ajax": false, "filename": "ProjectForm.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 15.23, "width_percent": 9.052}, {"sql": "select `id` from `re_features` inner join `re_project_features` on `re_features`.`id` = `re_project_features`.`feature_id` where `re_project_features`.`project_id` = 25", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/ProjectForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\ProjectForm.php", "line": 64}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}, {"index": 19, "namespace": null, "name": "platform/packages/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\FormBuilder.php", "line": 44}, {"index": 20, "namespace": null, "name": "platform/core/base/src/Forms/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormBuilder.php", "line": 11}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 570}], "start": **********.895758, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "ProjectForm.php:64", "source": {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/ProjectForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\ProjectForm.php", "line": 64}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FForms%2FProjectForm.php:64", "ajax": false, "filename": "ProjectForm.php", "line": "64"}, "connection": "xmetr", "explain": null, "start_percent": 24.282, "width_percent": 4.31}, {"sql": "select `id`, `name` from `re_features`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/ProjectForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\ProjectForm.php", "line": 67}, {"index": 17, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}, {"index": 18, "namespace": null, "name": "platform/packages/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\FormBuilder.php", "line": 44}, {"index": 19, "namespace": null, "name": "platform/core/base/src/Forms/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormBuilder.php", "line": 11}], "start": **********.898945, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 28.592, "width_percent": 2.107}, {"sql": "select `id`, `name` from `re_facilities`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/ProjectForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\ProjectForm.php", "line": 69}, {"index": 17, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}, {"index": 18, "namespace": null, "name": "platform/packages/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\FormBuilder.php", "line": 44}, {"index": 19, "namespace": null, "name": "platform/core/base/src/Forms/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormBuilder.php", "line": 11}], "start": **********.901483, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 30.699, "width_percent": 4.119}, {"sql": "select `re_facilities`.`id`, `distance`, `re_facilities_distances`.`reference_id` as `pivot_reference_id`, `re_facilities_distances`.`facility_id` as `pivot_facility_id`, `re_facilities_distances`.`reference_type` as `pivot_reference_type`, `re_facilities_distances`.`distance` as `pivot_distance` from `re_facilities` inner join `re_facilities_distances` on `re_facilities`.`id` = `re_facilities_distances`.`facility_id` where `re_facilities_distances`.`reference_id` = 25 and `re_facilities_distances`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Project'", "type": "query", "params": [], "bindings": [25, "Xmetr\\RealEstate\\Models\\Project"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/ProjectForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\ProjectForm.php", "line": 76}, {"index": 16, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}, {"index": 17, "namespace": null, "name": "platform/packages/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\FormBuilder.php", "line": 44}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormBuilder.php", "line": 11}, {"index": 19, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 570}], "start": **********.905029, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "ProjectForm.php:76", "source": {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/ProjectForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\ProjectForm.php", "line": 76}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FForms%2FProjectForm.php:76", "ajax": false, "filename": "ProjectForm.php", "line": "76"}, "connection": "xmetr", "explain": null, "start_percent": 34.818, "width_percent": 2.634}, {"sql": "select `category_id` from `re_categories` inner join `re_project_categories` on `re_categories`.`id` = `re_project_categories`.`category_id` where `re_project_categories`.`project_id` = 25", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/ProjectForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\ProjectForm.php", "line": 104}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}, {"index": 19, "namespace": null, "name": "platform/packages/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\FormBuilder.php", "line": 44}, {"index": 20, "namespace": null, "name": "platform/core/base/src/Forms/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormBuilder.php", "line": 11}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 570}], "start": **********.906709, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "ProjectForm.php:104", "source": {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/ProjectForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\ProjectForm.php", "line": 104}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FForms%2FProjectForm.php:104", "ajax": false, "filename": "ProjectForm.php", "line": "104"}, "connection": "xmetr", "explain": null, "start_percent": 37.452, "width_percent": 3.64}, {"sql": "select `name`, `id`, `type` from `re_custom_fields`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/ProjectForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\ProjectForm.php", "line": 111}, {"index": 17, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}, {"index": 18, "namespace": null, "name": "platform/packages/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\FormBuilder.php", "line": 44}, {"index": 19, "namespace": null, "name": "platform/core/base/src/Forms/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormBuilder.php", "line": 11}], "start": **********.909259, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 41.092, "width_percent": 2.155}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = 167 limit 1", "type": "query", "params": [], "bindings": [167], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/ProjectForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\ProjectForm.php", "line": 367}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}, {"index": 25, "namespace": null, "name": "platform/packages/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\FormBuilder.php", "line": 44}], "start": **********.14982, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 43.247, "width_percent": 4.215}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_id` in (25) and `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Project'", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Project"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Traits/Forms/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Traits\\Forms\\HasMetadata.php", "line": 49}, {"index": 25, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 305}, {"index": 26, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/ProjectController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\ProjectController.php", "line": 91}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.183071, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 47.462, "width_percent": 4.215}, {"sql": "select `lang_name` from `languages` where `lang_code` = 'en_US' limit 1", "type": "query", "params": [], "bindings": ["en_US"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/plugins/language-advanced/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Providers\\HookServiceProvider.php", "line": 163}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.911264, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 51.676, "width_percent": 3.784}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Project' and `slugs`.`reference_id` = 25 and `slugs`.`reference_id` is not null limit 1", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Project", 25], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 95}, {"index": 29, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 30, "namespace": "view", "name": "packages/slug::forms.fields.permalink", "file": "D:\\laragon\\www\\xmetr\\platform/packages/slug/resources/views/forms/fields/permalink.blade.php", "line": 23}], "start": **********.5851738, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 55.46, "width_percent": 3.688}, {"sql": "select `name`, `id` from `countries` order by `order` asc, `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/location/src/Fields/SelectLocationField.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\location\\src\\Fields\\SelectLocationField.php", "line": 77}, {"index": 17, "namespace": null, "name": "platform/plugins/location/src/Fields/SelectLocationField.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\location\\src\\Fields\\SelectLocationField.php", "line": 255}, {"index": 18, "namespace": "view", "name": "core/base::forms.form", "file": "D:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/form.blade.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1753140075.503424, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 59.148, "width_percent": 7.471}, {"sql": "select `name`, `id` from `states` where `country_id` = 29 order by `order` asc, `name` asc", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/location/src/Fields/SelectLocationField.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\location\\src\\Fields\\SelectLocationField.php", "line": 129}, {"index": 17, "namespace": null, "name": "platform/plugins/location/src/Fields/SelectLocationField.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\location\\src\\Fields\\SelectLocationField.php", "line": 260}, {"index": 18, "namespace": "view", "name": "core/base::forms.form", "file": "D:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/form.blade.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1753140075.802065, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 66.619, "width_percent": 2.586}, {"sql": "select `name`, `id` from `cities` where `country_id` = 29 order by `order` asc, `name` asc", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/location/src/Fields/SelectLocationField.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\location\\src\\Fields\\SelectLocationField.php", "line": 171}, {"index": 17, "namespace": null, "name": "platform/plugins/location/src/Fields/SelectLocationField.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\location\\src\\Fields\\SelectLocationField.php", "line": 264}, {"index": 18, "namespace": "view", "name": "core/base::forms.form", "file": "D:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/form.blade.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1753140075.8176332, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 69.205, "width_percent": 2.347}, {"sql": "select `name`, `id` from `districts` where `city_id` = 8204 order by `order` asc, `name` asc", "type": "query", "params": [], "bindings": [8204], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/location/src/Fields/SelectLocationField.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\location\\src\\Fields\\SelectLocationField.php", "line": 205}, {"index": 17, "namespace": null, "name": "platform/plugins/location/src/Fields/SelectLocationField.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\location\\src\\Fields\\SelectLocationField.php", "line": 268}, {"index": 18, "namespace": "view", "name": "core/base::forms.form", "file": "D:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/form.blade.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1753140075.8383799, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 71.552, "width_percent": 2.826}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'seo_meta' and `reference_id` = 25 and `reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Project') limit 1", "type": "query", "params": [], "bindings": ["seo_meta", 25, "Xmetr\\RealEstate\\Models\\Project"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 186}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 168}, {"index": 20, "namespace": null, "name": "platform/packages/seo-helper/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\seo-helper\\src\\Providers\\HookServiceProvider.php", "line": 65}, {"index": 27, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}], "start": **********.687587, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 74.377, "width_percent": 2.634}, {"sql": "select `lang_code`, `lang_flag`, `lang_name` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language-advanced/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Providers\\HookServiceProvider.php", "line": 69}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.723852, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 77.011, "width_percent": 17.146}, {"sql": "select `lang_flag`, `lang_name`, `lang_code` from `languages` where `lang_is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 938}, {"index": 19, "namespace": null, "name": "platform/plugins/language-advanced/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Providers\\HookServiceProvider.php", "line": 102}, {"index": 26, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.729867, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 94.157, "width_percent": 1.724}, {"sql": "select * from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 81}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.923122, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 95.881, "width_percent": 1.724}, {"sql": "select count(*) as aggregate from `re_consults` where `status` = 'unread'", "type": "query", "params": [], "bindings": ["unread"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Providers\\HookServiceProvider.php", "line": 721}, {"index": 20, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.322807, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "HookServiceProvider.php:721", "source": {"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Providers\\HookServiceProvider.php", "line": 721}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FProviders%2FHookServiceProvider.php:721", "ajax": false, "filename": "HookServiceProvider.php", "line": "721"}, "connection": "xmetr", "explain": null, "start_percent": 97.605, "width_percent": 2.395}]}, "models": {"data": {"Xmetr\\Location\\Models\\Country": {"value": 168, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FModels%2FCountry.php:1", "ajax": false, "filename": "Country.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Currency": {"value": 43, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FCurrency.php:1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Feature": {"value": 31, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FFeature.php:1", "ajax": false, "filename": "Feature.php", "line": "?"}}, "Xmetr\\Location\\Models\\District": {"value": 30, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FModels%2FDistrict.php:1", "ajax": false, "filename": "District.php", "line": "?"}}, "Xmetr\\Location\\Models\\City": {"value": 27, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FModels%2FCity.php:1", "ajax": false, "filename": "City.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Facility": {"value": 23, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FFacility.php:1", "ajax": false, "filename": "Facility.php", "line": "?"}}, "Xmetr\\Location\\Models\\State": {"value": 17, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FModels%2FState.php:1", "ajax": false, "filename": "State.php", "line": "?"}}, "Xmetr\\Language\\Models\\Language": {"value": 12, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php:1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Xmetr\\Base\\Models\\MetaBox": {"value": 2, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FMetaBox.php:1", "ajax": false, "filename": "MetaBox.php", "line": "?"}}, "Xmetr\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Project": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FProject.php:1", "ajax": false, "filename": "Project.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Account": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FAccount.php:1", "ajax": false, "filename": "Account.php", "line": "?"}}, "Xmetr\\Slug\\Models\\Slug": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FModels%2FSlug.php:1", "ajax": false, "filename": "Slug.php", "line": "?"}}}, "count": 357, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://xmetr.gc/admin/real-estate/projects/edit/25", "action_name": "project.edit", "controller_action": "Xmetr\\RealEstate\\Http\\Controllers\\ProjectController@edit", "uri": "GET admin/real-estate/projects/edit/{project}", "controller": "Xmetr\\RealEstate\\Http\\Controllers\\ProjectController@edit<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FHttp%2FControllers%2FProjectController.php:80\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Xmetr\\RealEstate\\Http\\Controllers", "prefix": "admin/real-estate/projects", "file": "<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FHttp%2FControllers%2FProjectController.php:80\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/real-estate/src/Http/Controllers/ProjectController.php:80-92</a>", "middleware": "web, core, auth", "duration": "13.69s", "peak_memory": "74MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-748265537 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-748265537\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-791397391 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-791397391\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-795216037 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">https://xmetr.gc/admin/real-estate/projects</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1708 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjE5eVFoNXI3S3dCZTVpWVUvb1ZlTkE9PSIsInZhbHVlIjoiVFFEOHg1MHRNUCtIQndDL1FUdThDSVJxSzFQTTlRRWNjUGJtalZRTmpvRFQrQ1UybGVrWnR6dUN0ZU16VVJXRWg0UVg5cFdaQmlmL212RUdPSjJLcUgray8wa3JyeFU3LzUrRUs3by9jVXM1cCt0MDZCTXZjczI2RTRNR0xqeTBJT1dpcWNNQTdEeW8wYkZ1S3lUc1pZSVFCd2FiN2EvdTRmZGdiU2FOYVREVUtPdGw5SG1xOUZHVU5Db0Zwa1lvVTRXRDFPOXBhNnZOQ3hkWjJjU2JtaDRQRU9WYXRwczhPTmM1Wit2aThBcz0iLCJtYWMiOiI0NThmZWI3NDI2NThmMTIxOWRlN2JiMjljY2Y3MjQzMDUzNmQyYjA5NzllZTg4YmY2ZTc5MGExYjEzODJhMGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.2064844405.1743885089; cookie_for_consent=1; _hjSessionUser_6417422=eyJpZCI6IjhkMzA2ODc1LTcwNjctNTU3Yi1hNDljLTdhNmEyNTdkM2QxOCIsImNyZWF0ZWQiOjE3NDgzODAwMDM5NTcsImV4aXN0aW5nIjp0cnVlfQ==; wishlist=657; project_wishlist=25; _hjSession_6417422=eyJpZCI6ImE2NTNhYzcwLTcyYjYtNDA5MC05N2RjLTVhOTkxZmEwOWNmOCIsImMiOjE3NTMxMzk4OTYzODEsInMiOjAsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjowfQ==; _ga_KQ6X76DET4=GS2.1.s1753139896$o98$g1$t1753139998$j60$l0$h0; XSRF-TOKEN=eyJpdiI6IkY3bkVSNjl3WVdoSC9jbkpITFRlSlE9PSIsInZhbHVlIjoiT0tUMi9HSExiK2RvOUd0Q1VBWmFBTERNL0psRW44RThaR3VSa1FKWDBranR4bVBXOXd4V0NxYkI5dDZFcW5OK1F4TjV4TzlmUjlNTHZHZE1MTDduYUFCbEI1T0ZaZmxvQ2pNN2swbkVBeDJEOGpPZlVMQ3F1YUs0RlBLT1JjTVoiLCJtYWMiOiJjZjYzZjQ5MjFkZmZlM2QzOTEzMDc4MzUxZTcyOWU4OTVlY2FhYjY0N2Y3MzhlMzAxMzNjYmFmNDFkYmIzZDY0IiwidGFnIjoiIn0%3D; xmetr_session=eyJpdiI6Imk0MnNNQUdWL0c4MGFZQzQ1ZFdUM2c9PSIsInZhbHVlIjoiQW1qcENjOUFsdnpZSnhpQytDQkFtNE9YdXZYZ2duMUxxdzEramJZeXZSbDNBR2xabkRsLzFnaWhyL1htSVpzYVVwYmdRZ2VvVExIVWhtNDRBaGQ2UlNZd0pha2JtYmYyNUt5Q2NRK3psTW9zUzJQS3B6akJBalRqd1h1aTFMaEsiLCJtYWMiOiI0YjE5MzIzZDk0ZjE3ODlhODI4MDFhNGNkMjU1MjI4ZjE3OWJmN2U5NzNhNDA1YjAzZjY5NWZkOGQzMzkxNzExIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-795216037\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2078115631 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|1uQwj4bI0uiYsJi0APplQif6Zji5Zq40vBOmafc1oj6NYK3vu89uqM3GLC7O|$2y$04$Astns3SXblBceX03zRicBuVKoyIRRFaOY1GuvREgnRYhHEelfNbdu</span>\"\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>_hjSessionUser_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>project_wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_hjSession_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_KQ6X76DET4</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">l5qhz74nLu14k6R5EhwDdzrUh3tMqqmO2SkqCqmk</span>\"\n  \"<span class=sf-dump-key>xmetr_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Jp72zRsSHdQFtXbAIt8wcrnAlDPz7vFvw5giRT0V</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2078115631\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1020083592 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 23:21:21 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1020083592\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1484606564 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">l5qhz74nLu14k6R5EhwDdzrUh3tMqqmO2SkqCqmk</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">https://xmetr.gc/admin/real-estate/projects</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1484606564\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://xmetr.gc/admin/real-estate/projects/edit/25", "action_name": "project.edit", "controller_action": "Xmetr\\RealEstate\\Http\\Controllers\\ProjectController@edit"}, "badge": null}}