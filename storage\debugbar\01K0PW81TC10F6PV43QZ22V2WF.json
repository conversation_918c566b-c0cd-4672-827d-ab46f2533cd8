{"__meta": {"id": "01K0PW81TC10F6PV43QZ22V2WF", "datetime": "2025-07-21 16:14:38", "utime": **********.413624, "method": "GET", "uri": "/admin/audit-logs/widgets/activities", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753114476.862962, "end": **********.413641, "duration": 1.5506789684295654, "duration_str": "1.55s", "measures": [{"label": "Booting", "start": 1753114476.862962, "relative_start": 0, "end": **********.580599, "relative_end": **********.580599, "duration": 0.****************, "duration_str": "718ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.58061, "relative_start": 0.****************, "end": **********.413644, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "833ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.596481, "relative_start": 0.****************, "end": **********.61244, "relative_end": **********.61244, "duration": 0.*****************, "duration_str": "15.96ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: plugins/audit-log::widgets.activities", "start": **********.677797, "relative_start": 0.****************, "end": **********.677797, "relative_end": **********.677797, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/audit-log::activity-line", "start": **********.206304, "relative_start": 1.****************, "end": **********.206304, "relative_end": **********.206304, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "start": **********.334263, "relative_start": 1.4713010787963867, "end": **********.334263, "relative_end": **********.334263, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.row", "start": **********.334651, "relative_start": 1.471688985824585, "end": **********.334651, "relative_end": **********.334651, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/audit-log::activity-line", "start": **********.334979, "relative_start": 1.4720170497894287, "end": **********.334979, "relative_end": **********.334979, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "start": **********.337346, "relative_start": 1.474384069442749, "end": **********.337346, "relative_end": **********.337346, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.row", "start": **********.337616, "relative_start": 1.474653959274292, "end": **********.337616, "relative_end": **********.337616, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/audit-log::activity-line", "start": **********.337869, "relative_start": 1.4749069213867188, "end": **********.337869, "relative_end": **********.337869, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "start": **********.34014, "relative_start": 1.4771780967712402, "end": **********.34014, "relative_end": **********.34014, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.row", "start": **********.340383, "relative_start": 1.4774210453033447, "end": **********.340383, "relative_end": **********.340383, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/audit-log::activity-line", "start": **********.340632, "relative_start": 1.4776699542999268, "end": **********.340632, "relative_end": **********.340632, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "start": **********.342924, "relative_start": 1.4799621105194092, "end": **********.342924, "relative_end": **********.342924, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.row", "start": **********.343188, "relative_start": 1.4802260398864746, "end": **********.343188, "relative_end": **********.343188, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/audit-log::activity-line", "start": **********.34344, "relative_start": 1.480478048324585, "end": **********.34344, "relative_end": **********.34344, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "start": **********.345742, "relative_start": 1.4827799797058105, "end": **********.345742, "relative_end": **********.345742, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.row", "start": **********.346012, "relative_start": 1.4830501079559326, "end": **********.346012, "relative_end": **********.346012, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/audit-log::activity-line", "start": **********.346358, "relative_start": 1.483396053314209, "end": **********.346358, "relative_end": **********.346358, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "start": **********.350779, "relative_start": 1.4878170490264893, "end": **********.350779, "relative_end": **********.350779, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.row", "start": **********.351067, "relative_start": 1.488105058670044, "end": **********.351067, "relative_end": **********.351067, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/audit-log::activity-line", "start": **********.351314, "relative_start": 1.4883520603179932, "end": **********.351314, "relative_end": **********.351314, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "start": **********.353521, "relative_start": 1.4905591011047363, "end": **********.353521, "relative_end": **********.353521, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.row", "start": **********.35375, "relative_start": 1.4907879829406738, "end": **********.35375, "relative_end": **********.35375, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/audit-log::activity-line", "start": **********.35399, "relative_start": 1.491028070449829, "end": **********.35399, "relative_end": **********.35399, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "start": **********.356075, "relative_start": 1.4931130409240723, "end": **********.356075, "relative_end": **********.356075, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.row", "start": **********.356307, "relative_start": 1.493345022201538, "end": **********.356307, "relative_end": **********.356307, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/audit-log::activity-line", "start": **********.356543, "relative_start": 1.4935810565948486, "end": **********.356543, "relative_end": **********.356543, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "start": **********.358661, "relative_start": 1.4956989288330078, "end": **********.358661, "relative_end": **********.358661, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.row", "start": **********.358889, "relative_start": 1.495927095413208, "end": **********.358889, "relative_end": **********.358889, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/audit-log::activity-line", "start": **********.359124, "relative_start": 1.496161937713623, "end": **********.359124, "relative_end": **********.359124, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "start": **********.361238, "relative_start": 1.4982759952545166, "end": **********.361238, "relative_end": **********.361238, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.row", "start": **********.361465, "relative_start": 1.4985029697418213, "end": **********.361465, "relative_end": **********.361465, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.index", "start": **********.361754, "relative_start": 1.4987919330596924, "end": **********.361754, "relative_end": **********.361754, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.index", "start": **********.362289, "relative_start": 1.4993269443511963, "end": **********.362289, "relative_end": **********.362289, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.simple-pagination", "start": **********.363493, "relative_start": 1.5005309581756592, "end": **********.363493, "relative_end": **********.363493, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b4c13ee47c8273ac628dba702f336180", "start": **********.378655, "relative_start": 1.515692949295044, "end": **********.378655, "relative_end": **********.378655, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::98abfcd8227e681cb9ad72e7d1720a64", "start": **********.40723, "relative_start": 1.****************, "end": **********.40723, "relative_end": **********.40723, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::card.footer.index", "start": **********.407959, "relative_start": 1.544996976852417, "end": **********.407959, "relative_end": **********.407959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.409345, "relative_start": 1.****************, "end": **********.411459, "relative_end": **********.411459, "duration": 0.0021140575408935547, "duration_str": "2.11ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 44573352, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "xmetr.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 37, "nb_templates": 37, "templates": [{"name": "plugins/audit-log::widgets.activities", "param_count": null, "params": [], "start": **********.677769, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/audit-log/resources/views/widgets/activities.blade.phpplugins/audit-log::widgets.activities", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fresources%2Fviews%2Fwidgets%2Factivities.blade.php:1", "ajax": false, "filename": "activities.blade.php", "line": "?"}}, {"name": "plugins/audit-log::activity-line", "param_count": null, "params": [], "start": **********.206279, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/audit-log/resources/views/activity-line.blade.phpplugins/audit-log::activity-line", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fresources%2Fviews%2Factivity-line.blade.php:1", "ajax": false, "filename": "activity-line.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "param_count": null, "params": [], "start": **********.334236, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/cell.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.cell", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php:1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.row", "param_count": null, "params": [], "start": **********.334631, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/row.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.row", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php:1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "plugins/audit-log::activity-line", "param_count": null, "params": [], "start": **********.334961, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/audit-log/resources/views/activity-line.blade.phpplugins/audit-log::activity-line", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fresources%2Fviews%2Factivity-line.blade.php:1", "ajax": false, "filename": "activity-line.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "param_count": null, "params": [], "start": **********.337324, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/cell.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.cell", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php:1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.row", "param_count": null, "params": [], "start": **********.337589, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/row.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.row", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php:1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "plugins/audit-log::activity-line", "param_count": null, "params": [], "start": **********.337851, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/audit-log/resources/views/activity-line.blade.phpplugins/audit-log::activity-line", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fresources%2Fviews%2Factivity-line.blade.php:1", "ajax": false, "filename": "activity-line.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "param_count": null, "params": [], "start": **********.340117, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/cell.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.cell", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php:1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.row", "param_count": null, "params": [], "start": **********.340364, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/row.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.row", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php:1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "plugins/audit-log::activity-line", "param_count": null, "params": [], "start": **********.340614, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/audit-log/resources/views/activity-line.blade.phpplugins/audit-log::activity-line", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fresources%2Fviews%2Factivity-line.blade.php:1", "ajax": false, "filename": "activity-line.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "param_count": null, "params": [], "start": **********.342901, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/cell.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.cell", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php:1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.row", "param_count": null, "params": [], "start": **********.343161, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/row.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.row", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php:1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "plugins/audit-log::activity-line", "param_count": null, "params": [], "start": **********.343422, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/audit-log/resources/views/activity-line.blade.phpplugins/audit-log::activity-line", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fresources%2Fviews%2Factivity-line.blade.php:1", "ajax": false, "filename": "activity-line.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "param_count": null, "params": [], "start": **********.34572, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/cell.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.cell", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php:1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.row", "param_count": null, "params": [], "start": **********.345993, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/row.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.row", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php:1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "plugins/audit-log::activity-line", "param_count": null, "params": [], "start": **********.346328, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/audit-log/resources/views/activity-line.blade.phpplugins/audit-log::activity-line", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fresources%2Fviews%2Factivity-line.blade.php:1", "ajax": false, "filename": "activity-line.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "param_count": null, "params": [], "start": **********.350756, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/cell.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.cell", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php:1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.row", "param_count": null, "params": [], "start": **********.351039, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/row.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.row", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php:1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "plugins/audit-log::activity-line", "param_count": null, "params": [], "start": **********.351297, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/audit-log/resources/views/activity-line.blade.phpplugins/audit-log::activity-line", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fresources%2Fviews%2Factivity-line.blade.php:1", "ajax": false, "filename": "activity-line.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "param_count": null, "params": [], "start": **********.3535, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/cell.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.cell", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php:1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.row", "param_count": null, "params": [], "start": **********.353733, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/row.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.row", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php:1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "plugins/audit-log::activity-line", "param_count": null, "params": [], "start": **********.353972, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/audit-log/resources/views/activity-line.blade.phpplugins/audit-log::activity-line", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fresources%2Fviews%2Factivity-line.blade.php:1", "ajax": false, "filename": "activity-line.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "param_count": null, "params": [], "start": **********.356054, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/cell.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.cell", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php:1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.row", "param_count": null, "params": [], "start": **********.356288, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/row.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.row", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php:1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "plugins/audit-log::activity-line", "param_count": null, "params": [], "start": **********.356525, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/audit-log/resources/views/activity-line.blade.phpplugins/audit-log::activity-line", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fresources%2Fviews%2Factivity-line.blade.php:1", "ajax": false, "filename": "activity-line.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "param_count": null, "params": [], "start": **********.35864, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/cell.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.cell", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php:1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.row", "param_count": null, "params": [], "start": **********.358871, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/row.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.row", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php:1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "plugins/audit-log::activity-line", "param_count": null, "params": [], "start": **********.359106, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/audit-log/resources/views/activity-line.blade.phpplugins/audit-log::activity-line", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fresources%2Fviews%2Factivity-line.blade.php:1", "ajax": false, "filename": "activity-line.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "param_count": null, "params": [], "start": **********.361218, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/cell.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.cell", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php:1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.row", "param_count": null, "params": [], "start": **********.361446, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/row.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.row", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php:1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.index", "param_count": null, "params": [], "start": **********.361735, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/index.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.index", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Findex.blade.php:1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.index", "param_count": null, "params": [], "start": **********.362261, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/index.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.index", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Findex.blade.php:1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "core/base::components.simple-pagination", "param_count": null, "params": [], "start": **********.363472, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/simple-pagination.blade.phpcore/base::components.simple-pagination", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fsimple-pagination.blade.php:1", "ajax": false, "filename": "simple-pagination.blade.php", "line": "?"}}, {"name": "__components::b4c13ee47c8273ac628dba702f336180", "param_count": null, "params": [], "start": **********.378631, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/b4c13ee47c8273ac628dba702f336180.blade.php__components::b4c13ee47c8273ac628dba702f336180", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fb4c13ee47c8273ac628dba702f336180.blade.php:1", "ajax": false, "filename": "b4c13ee47c8273ac628dba702f336180.blade.php", "line": "?"}}, {"name": "__components::98abfcd8227e681cb9ad72e7d1720a64", "param_count": null, "params": [], "start": **********.407206, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/98abfcd8227e681cb9ad72e7d1720a64.blade.php__components::98abfcd8227e681cb9ad72e7d1720a64", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F98abfcd8227e681cb9ad72e7d1720a64.blade.php:1", "ajax": false, "filename": "98abfcd8227e681cb9ad72e7d1720a64.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::card.footer.index", "param_count": null, "params": [], "start": **********.407939, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/card/footer/index.blade.phpa74ad8dfacd4f985eb3977517615ce25::card.footer.index", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Ffooter%2Findex.blade.php:1", "ajax": false, "filename": "index.blade.php", "line": "?"}}]}, "queries": {"count": 4, "nb_statements": 4, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02906, "accumulated_duration_str": "29.06ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.6273, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 0, "width_percent": 1.583}, {"sql": "select count(*) as aggregate from `audit_histories`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/plugins/audit-log/src/Http/Controllers/AuditLogController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\audit-log\\src\\Http\\Controllers\\AuditLogController.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.6384401, "duration": 0.00694, "duration_str": "6.94ms", "memory": 0, "memory_str": null, "filename": "AuditLogController.php:22", "source": {"index": 16, "namespace": null, "name": "platform/plugins/audit-log/src/Http/Controllers/AuditLogController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\audit-log\\src\\Http\\Controllers\\AuditLogController.php", "line": 22}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fsrc%2FHttp%2FControllers%2FAuditLogController.php:22", "ajax": false, "filename": "AuditLogController.php", "line": "22"}, "connection": "xmetr", "explain": null, "start_percent": 1.583, "width_percent": 23.882}, {"sql": "select * from `audit_histories` order by `created_at` desc limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/audit-log/src/Http/Controllers/AuditLogController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\audit-log\\src\\Http\\Controllers\\AuditLogController.php", "line": 22}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.647896, "duration": 0.02122, "duration_str": "21.22ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 25.465, "width_percent": 73.021}, {"sql": "select * from `users` where `users`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/plugins/audit-log/src/Http/Controllers/AuditLogController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\audit-log\\src\\Http\\Controllers\\AuditLogController.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.672559, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 98.486, "width_percent": 1.514}]}, "models": {"data": {"Xmetr\\AuditLog\\Models\\AuditHistory": {"value": 10, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fsrc%2FModels%2FAuditHistory.php:1", "ajax": false, "filename": "AuditHistory.php", "line": "?"}}, "Xmetr\\ACL\\Models\\User": {"value": 2, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 12, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://xmetr.gc/admin/audit-logs/widgets/activities", "action_name": "audit-log.widget.activities", "controller_action": "Xmetr\\AuditLog\\Http\\Controllers\\AuditLogController@getWidgetActivities", "uri": "GET admin/audit-logs/widgets/activities", "permission": "audit-log.index", "controller": "Xmetr\\AuditLog\\Http\\Controllers\\AuditLogController@getWidgetActivities<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fsrc%2FHttp%2FControllers%2FAuditLogController.php:14\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Xmetr\\AuditLog\\Http\\Controllers", "prefix": "admin/audit-logs", "file": "<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fsrc%2FHttp%2FControllers%2FAuditLogController.php:14\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/audit-log/src/Http/Controllers/AuditLogController.php:14-27</a>", "middleware": "web, core, auth", "duration": "1.55s", "peak_memory": "56MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1637971161 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1637971161\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1291232434 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1291232434\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2007599872 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImxYaDJRSUlEUDhUemxjaHFYZWs0R2c9PSIsInZhbHVlIjoieEx6MUVFN1NGVlhmdTlSeVRrMUJPQVBjdlp3dUw2TENOQnNMaGpOeVVEWSs2aVE4TUlsOTIzeCtxL1g5ZjFONjFmS1ZWMm43bHBHZmNYT1RhT210eTlpMDNjaUtGS0I3VFp0V0lic3RkcmtSN0lGSmh5b21JdE9pTWd6KzBFNkQiLCJtYWMiOiJhOTcxZTMxMGM2M2I1ZGY1MDAyMWMzNTM0NmQzZGNjNzNlNDA0ZGMyNjYwNmNiMmEzZWJhOTEwNjUyODRmODQ3IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">https://xmetr.gc/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1708 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjE5eVFoNXI3S3dCZTVpWVUvb1ZlTkE9PSIsInZhbHVlIjoiVFFEOHg1MHRNUCtIQndDL1FUdThDSVJxSzFQTTlRRWNjUGJtalZRTmpvRFQrQ1UybGVrWnR6dUN0ZU16VVJXRWg0UVg5cFdaQmlmL212RUdPSjJLcUgray8wa3JyeFU3LzUrRUs3by9jVXM1cCt0MDZCTXZjczI2RTRNR0xqeTBJT1dpcWNNQTdEeW8wYkZ1S3lUc1pZSVFCd2FiN2EvdTRmZGdiU2FOYVREVUtPdGw5SG1xOUZHVU5Db0Zwa1lvVTRXRDFPOXBhNnZOQ3hkWjJjU2JtaDRQRU9WYXRwczhPTmM1Wit2aThBcz0iLCJtYWMiOiI0NThmZWI3NDI2NThmMTIxOWRlN2JiMjljY2Y3MjQzMDUzNmQyYjA5NzllZTg4YmY2ZTc5MGExYjEzODJhMGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.2064844405.1743885089; cookie_for_consent=1; _hjSessionUser_6417422=eyJpZCI6IjhkMzA2ODc1LTcwNjctNTU3Yi1hNDljLTdhNmEyNTdkM2QxOCIsImNyZWF0ZWQiOjE3NDgzODAwMDM5NTcsImV4aXN0aW5nIjp0cnVlfQ==; wishlist=657; project_wishlist=25; _hjSession_6417422=eyJpZCI6ImU4N2JmNjY1LTFlZDgtNGUxNi04MTkwLTdiM2JlNzQ3ZTQyNyIsImMiOjE3NTMxMTE3NTMxMDUsInMiOjEsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjowfQ==; XSRF-TOKEN=eyJpdiI6ImxYaDJRSUlEUDhUemxjaHFYZWs0R2c9PSIsInZhbHVlIjoieEx6MUVFN1NGVlhmdTlSeVRrMUJPQVBjdlp3dUw2TENOQnNMaGpOeVVEWSs2aVE4TUlsOTIzeCtxL1g5ZjFONjFmS1ZWMm43bHBHZmNYT1RhT210eTlpMDNjaUtGS0I3VFp0V0lic3RkcmtSN0lGSmh5b21JdE9pTWd6KzBFNkQiLCJtYWMiOiJhOTcxZTMxMGM2M2I1ZGY1MDAyMWMzNTM0NmQzZGNjNzNlNDA0ZGMyNjYwNmNiMmEzZWJhOTEwNjUyODRmODQ3IiwidGFnIjoiIn0%3D; xmetr_session=eyJpdiI6InVhSitiWjBuT2xRZWh4OHZSb0ZwUUE9PSIsInZhbHVlIjoiRjB0Q094VG1qd2Q2Z3ZRTnEwUGZmdXNlMGhsaUR0NVV0Y0tmcVdlMTRLWURpdldZcmdjS0FCRmszV0tQMGpHNXZxd1haYkVQM1FiVitQQVVaVTQ4MVpSTEJqbnljZHBQcWgvNFNvMWxTR1dIbTFscEUrd2xkTGtVckVDcGJ3UVoiLCJtYWMiOiJiMDhhMzc5ZjY3OWFlOTBkZmNiYTRiYTE4NzAwZDJkY2MyZmY3NjQ2MGExNWI1MWUyZGQwY2EzYTJjMjA5OTE1IiwidGFnIjoiIn0%3D; _ga_KQ6X76DET4=GS2.1.s1753111753$o96$g1$t1753114475$j60$l0$h0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2007599872\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1980130297 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|1uQwj4bI0uiYsJi0APplQif6Zji5Zq40vBOmafc1oj6NYK3vu89uqM3GLC7O|$2y$04$Astns3SXblBceX03zRicBuVKoyIRRFaOY1GuvREgnRYhHEelfNbdu</span>\"\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>_hjSessionUser_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>project_wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_hjSession_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YDL2lQzPTa4gsz67cUPYaCqZbupT4F5thSay3mUv</span>\"\n  \"<span class=sf-dump-key>xmetr_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">g5DPOPNskxhCNniidFHP5zlrLjBYB9hkBdYqGT5e</span>\"\n  \"<span class=sf-dump-key>_ga_KQ6X76DET4</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1980130297\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-574437012 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 16:14:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-574437012\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1682170915 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YDL2lQzPTa4gsz67cUPYaCqZbupT4F5thSay3mUv</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">https://xmetr.gc/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1682170915\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://xmetr.gc/admin/audit-logs/widgets/activities", "action_name": "audit-log.widget.activities", "controller_action": "Xmetr\\AuditLog\\Http\\Controllers\\AuditLogController@getWidgetActivities"}, "badge": null}}